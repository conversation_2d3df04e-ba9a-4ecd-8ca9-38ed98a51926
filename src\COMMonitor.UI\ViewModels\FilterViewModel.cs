using System.ComponentModel;
using System.Runtime.CompilerServices;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.UI.ViewModels
{
    public class FilterViewModel : INotifyPropertyChanged
    {
        private readonly IDataFilterService _filterService;
        private readonly ILogger<FilterViewModel> _logger;

        public FilterViewModel(IDataFilterService filterService)
        {
            _filterService = filterService ?? throw new ArgumentNullException(nameof(filterService));
            _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<FilterViewModel>();
            
            AvailablePresets = new BindingList<string>();
            InitializeDefaultValues();
        }

        private void InitializeDefaultValues()
        {
            _portName = string.Empty;
            _directionIndex = 0;
            _startTime = DateTime.Now.AddHours(-1);
            _endTime = DateTime.Now;
            _useStartTime = false;
            _useEndTime = false;
            _dataPattern = string.Empty;
            _caseSensitive = false;
            _useRegex = false;
            _isActive = false;
            _presetName = string.Empty;
            _statusText = "就绪";
            _matchCountText = "匹配: 0";
        }

        #region Properties

        private string _portName;
        public string PortName
        {
            get => _portName;
            set
            {
                if (SetProperty(ref _portName, value))
                {
                    OnPropertyChanged(nameof(PortName));
                }
            }
        }

        private int _directionIndex;
        public int DirectionIndex
        {
            get => _directionIndex;
            set
            {
                if (SetProperty(ref _directionIndex, value))
                {
                    OnPropertyChanged(nameof(DirectionIndex));
                }
            }
        }

        private DateTime _startTime;
        public DateTime StartTime
        {
            get => _startTime;
            set
            {
                if (SetProperty(ref _startTime, value))
                {
                    OnPropertyChanged(nameof(StartTime));
                }
            }
        }

        private bool _useStartTime;
        public bool UseStartTime
        {
            get => _useStartTime;
            set
            {
                if (SetProperty(ref _useStartTime, value))
                {
                    OnPropertyChanged(nameof(UseStartTime));
                }
            }
        }

        private DateTime _endTime;
        public DateTime EndTime
        {
            get => _endTime;
            set
            {
                if (SetProperty(ref _endTime, value))
                {
                    OnPropertyChanged(nameof(EndTime));
                }
            }
        }

        private bool _useEndTime;
        public bool UseEndTime
        {
            get => _useEndTime;
            set
            {
                if (SetProperty(ref _useEndTime, value))
                {
                    OnPropertyChanged(nameof(UseEndTime));
                }
            }
        }

        private string _dataPattern;
        public string DataPattern
        {
            get => _dataPattern;
            set
            {
                if (SetProperty(ref _dataPattern, value))
                {
                    OnPropertyChanged(nameof(DataPattern));
                }
            }
        }

        private bool _caseSensitive;
        public bool CaseSensitive
        {
            get => _caseSensitive;
            set
            {
                if (SetProperty(ref _caseSensitive, value))
                {
                    OnPropertyChanged(nameof(CaseSensitive));
                }
            }
        }

        private bool _useRegex;
        public bool UseRegex
        {
            get => _useRegex;
            set
            {
                if (SetProperty(ref _useRegex, value))
                {
                    OnPropertyChanged(nameof(UseRegex));
                }
            }
        }

        private bool _isActive;
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (SetProperty(ref _isActive, value))
                {
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        private string _presetName;
        public string PresetName
        {
            get => _presetName;
            set
            {
                if (SetProperty(ref _presetName, value))
                {
                    OnPropertyChanged(nameof(PresetName));
                }
            }
        }

        private string _statusText;
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (SetProperty(ref _statusText, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        private string _matchCountText;
        public string MatchCountText
        {
            get => _matchCountText;
            set
            {
                if (SetProperty(ref _matchCountText, value))
                {
                    OnPropertyChanged(nameof(MatchCountText));
                }
            }
        }

        public BindingList<string> AvailablePresets { get; }
        
        private string _selectedPreset;
        public string SelectedPreset
        {
            get => _selectedPreset;
            set
            {
                if (SetProperty(ref _selectedPreset, value))
                {
                    OnPropertyChanged(nameof(SelectedPreset));
                }
            }
        }

        #endregion

        #region Public Methods

        public async Task InitializeAsync()
        {
            try
            {
                await LoadPresetsAsync();
                StatusText = "过滤器已就绪";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing filter view model");
                StatusText = $"初始化错误: {ex.Message}";
            }
        }

        public FilterCriteria GetCurrentCriteria()
        {
            return new FilterCriteria
            {
                PortName = string.IsNullOrWhiteSpace(PortName) ? null : PortName,
                Direction = GetDirectionFromIndex(DirectionIndex),
                StartTime = UseStartTime ? StartTime : null,
                EndTime = UseEndTime ? EndTime : null,
                DataPattern = string.IsNullOrWhiteSpace(DataPattern) ? null : DataPattern,
                CaseSensitive = CaseSensitive,
                UseRegex = UseRegex,
                IsActive = IsActive
            };
        }

        public void SetCriteria(FilterCriteria criteria)
        {
            if (criteria == null) return;

            PortName = criteria.PortName ?? string.Empty;
            DirectionIndex = GetIndexFromDirection(criteria.Direction);
            UseStartTime = criteria.StartTime.HasValue;
            if (criteria.StartTime.HasValue)
                StartTime = criteria.StartTime.Value;
            UseEndTime = criteria.EndTime.HasValue;
            if (criteria.EndTime.HasValue)
                EndTime = criteria.EndTime.Value;
            DataPattern = criteria.DataPattern ?? string.Empty;
            CaseSensitive = criteria.CaseSensitive;
            UseRegex = criteria.UseRegex;
            IsActive = criteria.IsActive;
        }

        public async Task SavePresetAsync()
        {
            if (string.IsNullOrWhiteSpace(PresetName))
            {
                StatusText = "请输入预设名称";
                return;
            }

            try
            {
                var criteria = GetCurrentCriteria();
                await _filterService.SaveFilterPresetAsync(criteria, PresetName);
                await LoadPresetsAsync();
                StatusText = $"预设 '{PresetName}' 已保存";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving preset: {PresetName}", PresetName);
                StatusText = $"保存错误: {ex.Message}";
            }
        }

        public async Task LoadSelectedPresetAsync()
        {
            if (string.IsNullOrEmpty(SelectedPreset))
            {
                StatusText = "请选择要加载的预设";
                return;
            }

            try
            {
                var presetName = SelectedPreset;
                var presets = await _filterService.LoadFilterPresetsAsync();
                var preset = presets.FirstOrDefault(p => string.Equals(p.PortName, presetName, StringComparison.OrdinalIgnoreCase));
                
                if (preset != null)
                {
                    SetCriteria(preset);
                    PresetName = presetName;
                    StatusText = $"预设 '{presetName}' 已加载";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading preset");
                StatusText = $"加载错误: {ex.Message}";
            }
        }

        public async Task DeleteSelectedPresetAsync()
        {
            if (string.IsNullOrEmpty(SelectedPreset))
            {
                StatusText = "请选择要删除的预设";
                return;
            }

            try
            {
                var presetName = SelectedPreset;
                var result = MessageBox.Show(
                    $"确定要删除预设 '{presetName}' 吗？",
                    "确认删除",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _filterService.DeleteFilterPresetAsync(presetName);
                    await LoadPresetsAsync();
                    
                    if (AvailablePresets.Count > 0)
                        SelectedPreset = AvailablePresets[0];
                    
                    StatusText = $"预设 '{presetName}' 已删除";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting preset");
                StatusText = $"删除错误: {ex.Message}";
            }
        }

        public async Task ApplyFilterAsync()
        {
            try
            {
                StatusText = "应用过滤器...";
                // This will be handled by the MainViewModel
                StatusText = "过滤器已应用";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying filter");
                StatusText = $"应用错误: {ex.Message}";
            }
        }

        public void ClearFilter()
        {
            InitializeDefaultValues();
            StatusText = "过滤器已清除";
        }

        public async Task UpdatePreviewAsync()
        {
            try
            {
                var criteria = GetCurrentCriteria();
                if (!criteria.IsActive)
                {
                    MatchCountText = "匹配: 所有";
                    return;
                }

                // Preview count would require access to data, handled by parent
                MatchCountText = "匹配: 预览模式";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating preview");
                MatchCountText = "匹配: 错误";
            }
        }

        public async Task LoadPresetsAsync()
        {
            try
            {
                AvailablePresets.Clear();
                var presets = await _filterService.GetPresetNamesAsync();
                foreach (var preset in presets)
                {
                    AvailablePresets.Add(preset);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading preset names");
                StatusText = $"加载预设错误: {ex.Message}";
            }
        }

        public void SaveSettings()
        {
            // Save last used settings
            _logger.LogInformation("Filter settings saved");
        }

        public void UpdatePortList(IEnumerable<string> ports)
        {
            // This would be called by MainViewModel to update available ports
            _logger.LogInformation("Port list updated: {Count} ports", ports?.Count() ?? 0);
        }

        #endregion

        #region Private Methods

        private static DataDirection? GetDirectionFromIndex(int index)
        {
            return index switch
            {
                1 => DataDirection.Receive,
                2 => DataDirection.Transmit,
                _ => null
            };
        }

        private static int GetIndexFromDirection(DataDirection? direction)
        {
            return direction switch
            {
                DataDirection.Receive => 1,
                DataDirection.Transmit => 2,
                _ => 0
            };
        }

        private bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}