using System.IO.Ports;

namespace COMMonitor.Core.Models
{
    public class SerialPortConfig
    {
        public string PortName { get; set; } = string.Empty;
        public int BaudRate { get; set; } = 9600;
        public int DataBits { get; set; } = 8;
        public StopBits StopBits { get; set; } = StopBits.One;
        public Parity Parity { get; set; } = Parity.None;
        public Handshake Handshake { get; set; } = Handshake.None;
        public int ReadTimeout { get; set; } = 1000;
        public int WriteTimeout { get; set; } = 1000;
        public bool DtrEnable { get; set; } = false;
        public bool RtsEnable { get; set; } = false;

        public override string ToString()
        {
            return $"{PortName}:{BaudRate},{DataBits},{Parity},{StopBits}";
        }

        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(PortName) &&
                   BaudRate >= 300 && BaudRate <= 921600 &&
                   DataBits >= 5 && DataBits <= 8 &&
                   Enum.IsDefined(typeof(StopBits), StopBits) &&
                   Enum.IsDefined(typeof(Parity), Parity) &&
                   Enum.IsDefined(typeof(Handshake), Handshake);
        }
    }
}