using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using COMMonitor.UI.ViewModels;
using System.Windows.Forms;

namespace COMMonitor.UI
{
    public partial class FilterForm : Form
    {
        private readonly FilterViewModel _viewModel;
        private readonly Dictionary<Control, ToolTip> _toolTips = new();

        public FilterForm(IDataFilterService filterService)
        {
            _viewModel = new FilterViewModel(filterService);
            InitializeComponent();
            InitializeFilterForm();
            SetupDataBindings();
            SetupEventHandlers();
        }

        private void InitializeFilterForm()
        {
            this.Text = "数据过滤器";
            this.ClientSize = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // Main container
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                RowCount = 4,
                ColumnCount = 1
            };

            // Criteria Group
            var criteriaGroup = CreateCriteriaGroup();
            
            // Presets Group
            var presetsGroup = CreatePresetsGroup();
            
            // Buttons Panel
            var buttonsPanel = CreateButtonsPanel();
            
            // Status Bar
            var statusBar = CreateStatusBar();

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 60));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 25));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20));

            mainPanel.Controls.Add(criteriaGroup, 0, 0);
            mainPanel.Controls.Add(presetsGroup, 0, 1);
            mainPanel.Controls.Add(buttonsPanel, 0, 2);
            mainPanel.Controls.Add(statusBar, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateCriteriaGroup()
        {
            var group = new GroupBox
            {
                Text = "过滤条件",
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 4,
                Padding = new Padding(5)
            };

            // Port filter
            layout.Controls.Add(new Label { Text = "端口:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
            cmbPort = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            layout.Controls.Add(cmbPort, 1, 0);

            // Direction filter
            layout.Controls.Add(new Label { Text = "方向:", TextAlign = ContentAlignment.MiddleRight }, 2, 0);
            cmbDirection = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            cmbDirection.Items.AddRange(new[] { "全部", "接收", "发送" });
            cmbDirection.SelectedIndex = 0;
            layout.Controls.Add(cmbDirection, 3, 0);

            // Time range
            layout.Controls.Add(new Label { Text = "开始时间:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
            dtpStartTime = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss",
                ShowCheckBox = true
            };
            layout.Controls.Add(dtpStartTime, 1, 1);

            layout.Controls.Add(new Label { Text = "结束时间:", TextAlign = ContentAlignment.MiddleRight }, 2, 1);
            dtpEndTime = new DateTimePicker
            {
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss",
                ShowCheckBox = true
            };
            layout.Controls.Add(dtpEndTime, 3, 1);

            // Data pattern
            layout.Controls.Add(new Label { Text = "数据模式:", TextAlign = ContentAlignment.MiddleRight }, 0, 2);
            txtDataPattern = new TextBox();
            layout.Controls.Add(txtDataPattern, 1, 2);
            layout.SetColumnSpan(txtDataPattern, 3);

            // Options
            chkCaseSensitive = new CheckBox { Text = "区分大小写" };
            chkUseRegex = new CheckBox { Text = "使用正则表达式" };
            chkFilterActive = new CheckBox { Text = "启用过滤器" };

            var optionsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false
            };
            optionsPanel.Controls.AddRange(new Control[] { chkCaseSensitive, chkUseRegex, chkFilterActive });

            layout.Controls.Add(optionsPanel, 0, 3);
            layout.SetColumnSpan(optionsPanel, 4);

            group.Controls.Add(layout);
            return group;
        }

        private GroupBox CreatePresetsGroup()
        {
            var group = new GroupBox
            {
                Text = "预设管理",
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3
            };

            // Preset selection
            layout.Controls.Add(new Label { Text = "选择预设:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
            cmbPresets = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            layout.Controls.Add(cmbPresets, 1, 0);

            // New preset name
            layout.Controls.Add(new Label { Text = "预设名称:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
            txtPresetName = new TextBox();
            layout.Controls.Add(txtPresetName, 1, 1);

            // Preset buttons
            var presetButtonsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight
            };

            btnSavePreset = new Button { Text = "保存预设", Width = 80 };
            btnLoadPreset = new Button { Text = "加载预设", Width = 80 };
            btnDeletePreset = new Button { Text = "删除预设", Width = 80 };

            presetButtonsPanel.Controls.AddRange(new Control[] { btnSavePreset, btnLoadPreset, btnDeletePreset });
            layout.Controls.Add(presetButtonsPanel, 0, 2);
            layout.SetColumnSpan(presetButtonsPanel, 2);

            group.Controls.Add(layout);
            return group;
        }

        private Panel CreateButtonsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill
            };

            var buttonsLayout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(5)
            };

            btnApply = new Button { Text = "应用", Width = 80, DialogResult = DialogResult.OK };
            btnClear = new Button { Text = "清除", Width = 80 };
            btnCancel = new Button { Text = "取消", Width = 80, DialogResult = DialogResult.Cancel };

            buttonsLayout.Controls.AddRange(new Control[] { btnCancel, btnClear, btnApply });
            panel.Controls.Add(buttonsLayout);

            return panel;
        }

        private StatusStrip CreateStatusBar()
        {
            var statusStrip = new StatusStrip();
            lblStatus = new ToolStripStatusLabel
            {
                Text = "就绪",
                Spring = true
            };
            lblMatchCount = new ToolStripStatusLabel
            {
                Text = "匹配: 0",
                Width = 100
            };

            statusStrip.Items.Add(lblStatus);
            statusStrip.Items.Add(lblMatchCount);

            return statusStrip;
        }

        private void SetupLayout()
        {
            // Set tab order
            int tabIndex = 0;
            cmbPort.TabIndex = tabIndex++;
            cmbDirection.TabIndex = tabIndex++;
            dtpStartTime.TabIndex = tabIndex++;
            dtpEndTime.TabIndex = tabIndex++;
            txtDataPattern.TabIndex = tabIndex++;
            chkCaseSensitive.TabIndex = tabIndex++;
            chkUseRegex.TabIndex = tabIndex++;
            chkFilterActive.TabIndex = tabIndex++;
            cmbPresets.TabIndex = tabIndex++;
            txtPresetName.TabIndex = tabIndex++;
            btnSavePreset.TabIndex = tabIndex++;
            btnLoadPreset.TabIndex = tabIndex++;
            btnDeletePreset.TabIndex = tabIndex++;
            btnApply.TabIndex = tabIndex++;
            btnClear.TabIndex = tabIndex++;
            btnCancel.TabIndex = tabIndex++;
        }

        private void SetupDataBindings()
        {
            // Bind controls to view model
            cmbPort.DataBindings.Add("SelectedItem", _viewModel, "PortName", false, DataSourceUpdateMode.OnPropertyChanged);
            cmbDirection.DataBindings.Add("SelectedIndex", _viewModel, "DirectionIndex", false, DataSourceUpdateMode.OnPropertyChanged);
            dtpStartTime.DataBindings.Add("Value", _viewModel, "StartTime", false, DataSourceUpdateMode.OnPropertyChanged);
            dtpStartTime.DataBindings.Add("Checked", _viewModel, "UseStartTime", false, DataSourceUpdateMode.OnPropertyChanged);
            dtpEndTime.DataBindings.Add("Value", _viewModel, "EndTime", false, DataSourceUpdateMode.OnPropertyChanged);
            dtpEndTime.DataBindings.Add("Checked", _viewModel, "UseEndTime", false, DataSourceUpdateMode.OnPropertyChanged);
            txtDataPattern.DataBindings.Add("Text", _viewModel, "DataPattern", false, DataSourceUpdateMode.OnPropertyChanged);
            chkCaseSensitive.DataBindings.Add("Checked", _viewModel, "CaseSensitive", false, DataSourceUpdateMode.OnPropertyChanged);
            chkUseRegex.DataBindings.Add("Checked", _viewModel, "UseRegex", false, DataSourceUpdateMode.OnPropertyChanged);
            chkFilterActive.DataBindings.Add("Checked", _viewModel, "IsActive", false, DataSourceUpdateMode.OnPropertyChanged);
            txtPresetName.DataBindings.Add("Text", _viewModel, "PresetName", false, DataSourceUpdateMode.OnPropertyChanged);
            cmbPresets.DataSource = _viewModel.AvailablePresets;
            
            // Bind status - use property changed events instead of DataBindings for ToolStripStatusLabel
            _viewModel.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == "StatusText")
                    lblStatus.Text = _viewModel.StatusText;
                else if (e.PropertyName == "MatchCountText")
                    lblMatchCount.Text = _viewModel.MatchCountText;
            };
        }

        private void SetupEventHandlers()
        {
            // Preset management
            btnSavePreset.Click += async (s, e) => await _viewModel.SavePresetAsync();
            btnLoadPreset.Click += async (s, e) => await _viewModel.LoadSelectedPresetAsync();
            btnDeletePreset.Click += async (s, e) => await _viewModel.DeleteSelectedPresetAsync();

            // Filter actions
            btnApply.Click += async (s, e) => await _viewModel.ApplyFilterAsync();
            btnClear.Click += (s, e) => _viewModel.ClearFilter();
            
            // Real-time updates
            txtDataPattern.TextChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            cmbDirection.SelectedIndexChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            cmbPort.SelectedIndexChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            dtpStartTime.ValueChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            dtpEndTime.ValueChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            chkCaseSensitive.CheckedChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();
            chkUseRegex.CheckedChanged += async (s, e) => await _viewModel.UpdatePreviewAsync();

            // Form events
            this.Shown += async (s, e) => await _viewModel.InitializeAsync();
            this.FormClosing += (s, e) => _viewModel.SaveSettings();

            // Tooltips
            AddTooltip(cmbPort, "选择要过滤的COM端口，留空表示全部端口");
            AddTooltip(cmbDirection, "选择数据方向：全部、接收或发送");
            AddTooltip(dtpStartTime, "设置开始时间过滤，勾选复选框启用");
            AddTooltip(dtpEndTime, "设置结束时间过滤，勾选复选框启用");
            AddTooltip(txtDataPattern, "输入要搜索的数据模式，支持文本或十六进制");
            AddTooltip(chkUseRegex, "启用正则表达式模式匹配");
            AddTooltip(chkCaseSensitive, "启用大小写敏感匹配");
            AddTooltip(chkFilterActive, "启用/禁用过滤器");
        }

        private void AddTooltip(Control control, string text)
        {
            var tooltip = new ToolTip();
            _toolTips[control] = tooltip;
            tooltip.SetToolTip(control, text);
        }

        public FilterCriteria GetFilterCriteria()
        {
            return _viewModel.GetCurrentCriteria();
        }

        public bool IsFilterActive
        {
            get => _viewModel.IsActive;
        }

        // Control declarations
        private ComboBox cmbPort;
        private ComboBox cmbDirection;
        private DateTimePicker dtpStartTime;
        private DateTimePicker dtpEndTime;
        private TextBox txtDataPattern;
        private CheckBox chkCaseSensitive;
        private CheckBox chkUseRegex;
        private CheckBox chkFilterActive;
        private ComboBox cmbPresets;
        private TextBox txtPresetName;
        private Button btnSavePreset;
        private Button btnLoadPreset;
        private Button btnDeletePreset;
        private Button btnApply;
        private Button btnClear;
        private Button btnCancel;
        private ToolStripStatusLabel lblStatus;
        private ToolStripStatusLabel lblMatchCount;
    }
}