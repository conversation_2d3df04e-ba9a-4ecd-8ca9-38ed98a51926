using COMMonitor.Core.Interfaces;
using COMMonitor.Infrastructure.Services;
using COMMonitor.UI.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;
using System.Windows.Forms;

namespace COMMonitor.UI
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            ApplicationConfiguration.Initialize();

            var host = Host.CreateDefaultBuilder()
                .ConfigureServices(ConfigureServices)
                .ConfigureLogging(ConfigureLogging)
                .Build();

            using var serviceScope = host.Services.CreateScope();
            var services = serviceScope.ServiceProvider;

            try
            {
                var mainForm = services.GetRequiredService<MainForm>();
                Application.Run(mainForm);
            }
            catch (Exception ex)
            {
                try
                {
                    var loggerFactory = services.GetRequiredService<ILoggerFactory>();
                    var logger = loggerFactory.CreateLogger("Program");
                    logger.LogCritical(ex, "Application crashed");
                }
                catch
                {
                    // Fallback if services aren't available
                }
                
                MessageBox.Show(
                    $"应用程序发生严重错误：{ex.Message}\n\n详细信息请查看日志文件。",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private static void ConfigureServices(HostBuilderContext context, IServiceCollection services)
        {
            // Register core services
            services.AddSingleton<ISerialPortService, COMMonitor.Core.Services.SerialPortService>();
            services.AddSingleton<IDataStorageService, FileStorageService>();
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            services.AddSingleton<IDataFilterService, DataFilterService>();

            // Register UI services
            services.AddSingleton<MainForm>();
            services.AddTransient<PortConfigForm>();
            services.AddTransient<FilterForm>();
            services.AddTransient<LogViewerForm>();
            services.AddTransient<SettingsForm>();

            // Register view models
            services.AddSingleton<MainViewModel>();
        }

        private static void ConfigureLogging(ILoggingBuilder logging)
        {
            logging.ClearProviders();
            logging.AddNLog(new NLogProviderOptions
            {
                RemoveLoggerFactoryFilter = false
            });
        }
    }
}