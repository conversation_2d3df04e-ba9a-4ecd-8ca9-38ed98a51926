namespace COMMonitor.Core.Models
{
    public enum DataDirection
    {
        Receive,
        Transmit
    }

    public class SerialDataFrame
    {
        public DateTime Timestamp { get; set; }
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public DataDirection Direction { get; set; }
        public string PortName { get; set; } = string.Empty;
        public long SequenceNumber { get; set; }

        public int Length => Data?.Length ?? 0;

        public string DataAsText => Data != null ? System.Text.Encoding.ASCII.GetString(Data) : string.Empty;

        public string DataAsHex => Data != null ? BitConverter.ToString(Data).Replace("-", " ") : string.Empty;

        public override string ToString()
        {
            return $"[{Timestamp:HH:mm:ss.fff}] {PortName} {Direction}: {DataAsHex}";
        }
    }
}