{"format": 1, "restore": {"D:\\csharp\\COMmonitor\\src\\COMMonitor.Infrastructure\\COMMonitor.Infrastructure.csproj": {}}, "projects": {"D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\COMMonitor.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\COMMonitor.Core.csproj", "projectName": "COMMonitor.Core", "projectPath": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\COMMonitor.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[6.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\csharp\\COMmonitor\\src\\COMMonitor.Infrastructure\\COMMonitor.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Infrastructure\\COMMonitor.Infrastructure.csproj", "projectName": "COMMonitor.Infrastructure", "projectPath": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Infrastructure\\COMMonitor.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\COMMonitor.Core.csproj": {"projectPath": "D:\\csharp\\COMmonitor\\src\\COMMonitor.Core\\COMMonitor.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "NLog": {"target": "Package", "version": "[5.2.2, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}}