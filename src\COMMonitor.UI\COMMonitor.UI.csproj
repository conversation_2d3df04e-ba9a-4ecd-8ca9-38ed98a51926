<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>10.0</LangVersion>
    <!-- 添加程序集元数据 -->
    <AssemblyTitle>COM Monitor - Serial Port Monitor</AssemblyTitle>
    <AssemblyDescription>Professional serial port monitoring tool</AssemblyDescription>
    <AssemblyCompany>Your Company Name</AssemblyCompany>
    <AssemblyProduct>COM Monitor</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    
    <!-- 代码签名准备 -->
    <SignAssembly>false</SignAssembly>
    <DelaySign>false</DelaySign>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\COMMonitor.Core\COMMonitor.Core.csproj" />
    <ProjectReference Include="..\COMMonitor.Infrastructure\COMMonitor.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
