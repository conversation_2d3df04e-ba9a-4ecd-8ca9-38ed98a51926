<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>10.0</LangVersion>

    <!-- 程序集元数据 - 详细信息有助于避免误报 -->
    <AssemblyTitle>COM Monitor - Serial Port Monitor</AssemblyTitle>
    <AssemblyDescription>Professional serial port monitoring and debugging tool for Windows. Used for monitoring RS232/RS485 communication.</AssemblyDescription>
    <AssemblyCompany>Open Source Project</AssemblyCompany>
    <AssemblyProduct>COM Monitor</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 Open Source</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>

    <!-- 应用程序清单和安全设置 -->
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <Win32Resource />
    <ApplicationIcon />

    <!-- 代码签名准备 -->
    <SignAssembly>false</SignAssembly>
    <DelaySign>false</DelaySign>

    <!-- 构建优化 - 减少可疑行为 -->
    <DebugType>embedded</DebugType>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>

    <!-- 明确标识应用程序类型 -->
    <ApplicationType>Windows Application</ApplicationType>
    <StartupObject>COMMonitor.UI.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\COMMonitor.Core\COMMonitor.Core.csproj" />
    <ProjectReference Include="..\COMMonitor.Infrastructure\COMMonitor.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
