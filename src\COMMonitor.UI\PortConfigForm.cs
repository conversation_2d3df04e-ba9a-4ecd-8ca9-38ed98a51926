using COMMonitor.Core.Models;

namespace COMMonitor.UI
{
    public partial class PortConfigForm : Form
    {
        public SerialPortConfig Config { get; set; } = new();

        public PortConfigForm()
        {
            InitializeComponent();
        }

        private void PortConfigForm_Load(object sender, EventArgs e)
        {
            LoadConfiguration();
        }

        private void LoadConfiguration()
        {
            cmbPortName.Text = Config.PortName;
            cmbBaudRate.Text = Config.BaudRate.ToString();
            cmbDataBits.Text = Config.DataBits.ToString();
            cmbParity.SelectedItem = Config.Parity;
            cmbStopBits.SelectedItem = Config.StopBits;
            cmbHandshake.SelectedItem = Config.Handshake;
            chkDtrEnable.Checked = Config.DtrEnable;
            chkRtsEnable.Checked = Config.RtsEnable;
            txtReadTimeout.Text = Config.ReadTimeout.ToString();
            txtWriteTimeout.Text = Config.WriteTimeout.ToString();
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (ValidateConfiguration())
            {
                Config.PortName = cmbPortName.Text;
                Config.BaudRate = int.Parse(cmbBaudRate.Text);
                Config.DataBits = int.Parse(cmbDataBits.Text);
                Config.Parity = (System.IO.Ports.Parity)cmbParity.SelectedItem;
                Config.StopBits = (System.IO.Ports.StopBits)cmbStopBits.SelectedItem;
                Config.Handshake = (System.IO.Ports.Handshake)cmbHandshake.SelectedItem;
                Config.DtrEnable = chkDtrEnable.Checked;
                Config.RtsEnable = chkRtsEnable.Checked;
                Config.ReadTimeout = int.Parse(txtReadTimeout.Text);
                Config.WriteTimeout = int.Parse(txtWriteTimeout.Text);

                DialogResult = DialogResult.OK;
                Close();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private bool ValidateConfiguration()
        {
            if (string.IsNullOrWhiteSpace(cmbPortName.Text))
            {
                MessageBox.Show("请选择端口", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!int.TryParse(cmbBaudRate.Text, out var baudRate) || baudRate < 300 || baudRate > 921600)
            {
                MessageBox.Show("波特率必须在300-921600之间", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!int.TryParse(cmbDataBits.Text, out var dataBits) || dataBits < 5 || dataBits > 8)
            {
                MessageBox.Show("数据位必须在5-8之间", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!int.TryParse(txtReadTimeout.Text, out var readTimeout) || readTimeout < 100)
            {
                MessageBox.Show("读取超时必须大于等于100ms", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!int.TryParse(txtWriteTimeout.Text, out var writeTimeout) || writeTimeout < 100)
            {
                MessageBox.Show("写入超时必须大于等于100ms", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }


        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cmbHandshake;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox cmbStopBits;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox cmbParity;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox cmbDataBits;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox cmbBaudRate;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbPortName;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TextBox txtWriteTimeout;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox txtReadTimeout;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.CheckBox chkRtsEnable;
        private System.Windows.Forms.CheckBox chkDtrEnable;
        private System.Windows.Forms.Button btnOk;
        private System.Windows.Forms.Button btnCancel;
    }
}