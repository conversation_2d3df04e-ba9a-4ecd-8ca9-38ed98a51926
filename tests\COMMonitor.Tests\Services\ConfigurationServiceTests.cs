using System.Text.Json;
using COMMonitor.Core.Exceptions;
using COMMonitor.Core.Models;
using COMMonitor.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using Moq;

namespace COMMonitor.Tests.Services
{
    public class ConfigurationServiceTests : IDisposable
    {
        private readonly string _testConfigPath;
        private readonly ConfigurationService _service;
        private readonly Mock<ILogger<ConfigurationService>> _mockLogger;

        public ConfigurationServiceTests()
        {
            _testConfigPath = Path.Combine(Path.GetTempPath(), "COMMonitor_Test");
            Directory.CreateDirectory(_testConfigPath);
            
            _mockLogger = new Mock<ILogger<ConfigurationService>>();
            _service = new ConfigurationService(_mockLogger.Object);
        }

        [Fact]
        public async Task LoadConfigurationAsync_WhenFileDoesNotExist_ShouldCreateDefault()
        {
            // Act
            var config = await _service.LoadConfigurationAsync();

            // Assert
            Assert.NotNull(config);
            Assert.Equal("COM1", config.DefaultSerialConfig.PortName);
            Assert.Equal(9600, config.DefaultSerialConfig.BaudRate);
            Assert.True(_service.ConfigurationExists);
        }

        [Fact]
        public async Task SaveConfigurationAsync_WithValidConfig_ShouldSaveSuccessfully()
        {
            // Arrange
            var config = new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig
                {
                    PortName = "COM2",
                    BaudRate = 115200,
                    DataBits = 8,
                    StopBits = System.IO.Ports.StopBits.One,
                    Parity = System.IO.Ports.Parity.None
                },
                LogConfig = new LogFileConfig(),
                DisplayConfig = new DisplayConfig(),
                WindowConfig = new WindowConfig()
            };

            // Act
            await _service.SaveConfigurationAsync(config);

            // Assert
            var loadedConfig = await _service.LoadConfigurationAsync();
            Assert.Equal("COM2", loadedConfig.DefaultSerialConfig.PortName);
            Assert.Equal(115200, loadedConfig.DefaultSerialConfig.BaudRate);
        }

        [Fact]
        public async Task SaveConfigurationAsync_WithInvalidConfig_ShouldThrowException()
        {
            // Arrange
            var config = new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig { PortName = "" } // Invalid
            };

            // Act & Assert
            await Assert.ThrowsAsync<ConfigurationException>(() => _service.SaveConfigurationAsync(config));
        }

        [Fact]
        public async Task ResetConfigurationAsync_ShouldCreateDefaultConfig()
        {
            // Arrange - create a custom config first
            var customConfig = new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig { PortName = "COM3" }
            };
            await _service.SaveConfigurationAsync(customConfig);

            // Act
            var resetResult = await _service.ResetConfigurationAsync();

            // Assert
            Assert.True(resetResult);
            var config = await _service.LoadConfigurationAsync();
            Assert.Equal("COM1", config.DefaultSerialConfig.PortName);
        }

        [Fact]
        public async Task ExportConfigurationAsync_ShouldCreateFile()
        {
            // Arrange
            var config = new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig { PortName = "COM4" }
            };
            var exportPath = Path.Combine(_testConfigPath, "exported_config.json");

            // Act
            await _service.ExportConfigurationAsync(config, exportPath);

            // Assert
            Assert.True(File.Exists(exportPath));
            var content = await File.ReadAllTextAsync(exportPath);
            Assert.Contains("COM4", content);
        }

        [Fact]
        public async Task ImportConfigurationAsync_ShouldLoadFromFile()
        {
            // Arrange
            var config = new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig { PortName = "COM5" }
            };
            var importPath = Path.Combine(_testConfigPath, "import_config.json");
            await _service.ExportConfigurationAsync(config, importPath);

            // Act
            var importedConfig = await _service.ImportConfigurationAsync(importPath);

            // Assert
            Assert.Equal("COM5", importedConfig.DefaultSerialConfig.PortName);
        }

        [Fact]
        public async Task LoadConfigurationAsync_WithCorruptedFile_ShouldCreateBackupAndDefault()
        {
            // Arrange
            var corruptedContent = "{ invalid json }";
            await File.WriteAllTextAsync(_service.ConfigurationFilePath, corruptedContent);

            // Act
            var config = await _service.LoadConfigurationAsync();

            // Assert
            Assert.NotNull(config);
            Assert.Equal("COM1", config.DefaultSerialConfig.PortName);
            Assert.True(File.Exists(_service.ConfigurationFilePath));
        }

        public void Dispose()
        {
            try
            {
                if (Directory.Exists(_testConfigPath))
                {
                    Directory.Delete(_testConfigPath, true);
                }
            }
            catch
            {
                // Ignore cleanup errors in tests
            }
        }
    }
}