using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using COMMonitor.Core.Services;
using COMMonitor.UI.ViewModels;
using Microsoft.Extensions.Logging;
using Moq;

namespace COMMonitor.Tests.Integration
{
    public class FilterIntegrationTests : IDisposable
    {
        private readonly string _testPresetsPath;
        private readonly DataFilterService _filterService;
        private readonly FilterViewModel _filterViewModel;
        private readonly Mock<ILogger<DataFilterService>> _loggerMock;

        public FilterIntegrationTests()
        {
            var tempPath = Path.GetTempPath();
            _testPresetsPath = Path.Combine(tempPath, $"com_monitor_test_{Guid.NewGuid()}");
            Directory.CreateDirectory(_testPresetsPath);

            _loggerMock = new Mock<ILogger<DataFilterService>>();
            _filterService = new DataFilterService(_loggerMock.Object);
            _filterViewModel = new FilterViewModel(_filterService);
        }

        [Fact]
        public async Task EndToEndFilterWorkflow_Success()
        {
            // Arrange
            var testData = CreateTestData();
            var criteria = new FilterCriteria
            {
                PortName = "COM1",
                Direction = DataDirection.Receive,
                DataPattern = "hello",
                CaseSensitive = false,
                IsActive = true
            };

            // Act
            // Step 1: Apply filter
            var filteredData = await _filterService.ApplyFilterAsync(testData, criteria);

            // Step 2: Save as preset
            await _filterService.SaveFilterPresetAsync(criteria, "TestWorkflow");

            // Step 3: Load preset
            var presets = await _filterService.LoadFilterPresetsAsync();
            var loadedCriteria = presets.FirstOrDefault(p => p.PortName == "COM1");

            // Step 4: Apply loaded preset
            var reFilteredData = await _filterService.ApplyFilterAsync(testData, loadedCriteria);

            // Assert
            Assert.NotNull(loadedCriteria);
            Assert.Equal(filteredData.Count, reFilteredData.Count);
            Assert.All(filteredData, frame =>
            {
                Assert.Equal("COM1", frame.PortName);
                Assert.Equal(DataDirection.Receive, frame.Direction);
                Assert.Contains("hello", Encoding.ASCII.GetString(frame.Data).ToLower());
            });
        }

        [Fact]
        public async Task FilterViewModel_PresetManagement_Success()
        {
            // Arrange
            var criteria = new FilterCriteria
            {
                PortName = "COM3",
                UseRegex = true,
                DataPattern = "[A-Z]+"
            };

            // Act
            // Set criteria in view model
            _filterViewModel.SetCriteria(criteria);
            _filterViewModel.PresetName = "RegexTest";
            await _filterViewModel.SavePresetAsync();

            // Load presets
            await _filterViewModel.LoadPresetsAsync();

            // Select and load preset
            if (_filterViewModel.AvailablePresets.Count > 0)
            {
                await _filterViewModel.LoadSelectedPresetAsync();
            }

            // Get current criteria
            var currentCriteria = _filterViewModel.GetCurrentCriteria();

            // Assert
            Assert.Equal("COM3", currentCriteria.PortName);
            Assert.True(currentCriteria.UseRegex);
            Assert.Equal("[A-Z]+", currentCriteria.DataPattern);
            Assert.Contains("RegexTest", _filterViewModel.AvailablePresets);
        }

        [Fact]
        public async Task FilterService_PresetPersistence_SurvivesAppRestart()
        {
            // Arrange
            var criteria = new FilterCriteria
            {
                PortName = "COM5",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1),
                DataPattern = "persistent"
            };

            // Act
            // Save preset
            await _filterService.SaveFilterPresetAsync(criteria, "PersistentTest");

            // Create new service instance (simulating app restart)
            var newService = new DataFilterService(_loggerMock.Object);
            var presets = await newService.LoadFilterPresetsAsync();
            var loadedCriteria = presets.FirstOrDefault(p => p.DataPattern == "persistent");

            // Assert
            Assert.NotNull(loadedCriteria);
            Assert.Equal("COM5", loadedCriteria.PortName);
            Assert.Equal("persistent", loadedCriteria.DataPattern);
        }

        [Fact]
        public async Task FilterPerformance_LargeDataset_MeetsRequirements()
        {
            // Arrange
            var testData = GenerateLargeTestData(50000);
            var criteria = new FilterCriteria
            {
                PortName = "COM1",
                DataPattern = "performance"
            };

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await _filterService.ApplyFilterAsync(testData, criteria);
            stopwatch.Stop();

            // Assert
            Assert.True(stopwatch.ElapsedMilliseconds < 2000, 
                $"Filtering 50k records took {stopwatch.ElapsedMilliseconds}ms, exceeding 2s limit");
            Assert.NotEmpty(result);
        }

        [Fact]
        public async Task ComplexFilterCombination_AllCriteria_Success()
        {
            // Arrange
            var testData = CreateComplexTestData();
            var criteria = new FilterCriteria
            {
                PortName = "COM2",
                Direction = DataDirection.Transmit,
                StartTime = DateTime.Now.AddMinutes(-10),
                EndTime = DateTime.Now.AddMinutes(-5),
                DataPattern = "data",
                CaseSensitive = false,
                UseRegex = false,
                IsActive = true
            };

            // Act
            var result = await _filterService.ApplyFilterAsync(testData, criteria);

            // Assert
            Assert.NotEmpty(result);
            Assert.All(result, frame =>
            {
                Assert.Equal("COM2", frame.PortName);
                Assert.Equal(DataDirection.Transmit, frame.Direction);
                Assert.True(frame.Timestamp >= criteria.StartTime && frame.Timestamp <= criteria.EndTime);
                Assert.Contains("data", Encoding.ASCII.GetString(frame.Data).ToLower());
            });
        }

        [Fact]
        public async Task SearchFunctionality_CrossRepresentation_SearchInTextAndHex()
        {
            // Arrange
            var testData = new List<SerialDataFrame>
            {
                new SerialDataFrame
                {
                    Timestamp = DateTime.Now,
                    Data = Encoding.ASCII.GetBytes("Hello 123"),
                    Direction = DataDirection.Receive,
                    PortName = "COM1"
                },
                new SerialDataFrame
                {
                    Timestamp = DateTime.Now,
                    Data = new byte[] { 0x41, 0x42, 0x43 }, // "ABC" in hex
                    Direction = DataDirection.Transmit,
                    PortName = "COM2"
                }
            };

            // Act
            var textResult = await _filterService.SearchAsync(testData, "Hello");
            var hexResult = await _filterService.SearchAsync(testData, "41 42 43");

            // Assert
            Assert.Single(textResult);
            Assert.Single(hexResult);
            Assert.Contains("Hello", textResult[0].MatchText);
            Assert.Contains("41 42 43", hexResult[0].MatchText);
        }

        private List<SerialDataFrame> CreateTestData()
        {
            var baseTime = DateTime.Now.AddMinutes(-15);
            return new List<SerialDataFrame>
            {
                new SerialDataFrame
                {
                    Timestamp = baseTime.AddMinutes(1),
                    Data = Encoding.ASCII.GetBytes("Hello World"),
                    Direction = DataDirection.Receive,
                    PortName = "COM1"
                },
                new SerialDataFrame
                {
                    Timestamp = baseTime.AddMinutes(3),
                    Data = Encoding.ASCII.GetBytes("Test Data 123"),
                    Direction = DataDirection.Transmit,
                    PortName = "COM2"
                },
                new SerialDataFrame
                {
                    Timestamp = baseTime.AddMinutes(5),
                    Data = Encoding.ASCII.GetBytes("hello test"),
                    Direction = DataDirection.Receive,
                    PortName = "COM1"
                },
                new SerialDataFrame
                {
                    Timestamp = baseTime.AddMinutes(7),
                    Data = Encoding.ASCII.GetBytes("performance test data"),
                    Direction = DataDirection.Transmit,
                    PortName = "COM1"
                },
                new SerialDataFrame
                {
                    Timestamp = baseTime.AddMinutes(9),
                    Data = Encoding.ASCII.GetBytes("COM2 only data"),
                    Direction = DataDirection.Transmit,
                    PortName = "COM2"
                }
            };
        }

        private List<SerialDataFrame> CreateComplexTestData()
        {
            var now = DateTime.Now;
            var data = new List<SerialDataFrame>();

            for (int i = 0; i < 100; i++)
            {
                data.Add(new SerialDataFrame
                {
                    Timestamp = now.AddMinutes(-i),
                    Data = Encoding.ASCII.GetBytes($"Frame {i} data content"),
                    Direction = i % 3 == 0 ? DataDirection.Transmit : DataDirection.Receive,
                    PortName = $"COM{i % 4 + 1}"
                });
            }

            return data;
        }

        private List<SerialDataFrame> GenerateLargeTestData(int count)
        {
            var data = new List<SerialDataFrame>();
            var baseTime = DateTime.Now;

            for (int i = 0; i < count; i++)
            {
                data.Add(new SerialDataFrame
                {
                    Timestamp = baseTime.AddMilliseconds(-i),
                    Data = Encoding.ASCII.GetBytes($"Large dataset frame {i} with performance test data"),
                    Direction = i % 2 == 0 ? DataDirection.Receive : DataDirection.Transmit,
                    PortName = $"COM{i % 10 + 1}"
                });
            }

            return data;
        }

        public void Dispose()
        {
            // Clean up test files
            try
            {
                if (Directory.Exists(_testPresetsPath))
                {
                    Directory.Delete(_testPresetsPath, true);
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}