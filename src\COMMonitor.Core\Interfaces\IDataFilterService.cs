using COMMonitor.Core.Models;

namespace COMMonitor.Core.Interfaces
{
    public class FilterCriteria
    {
        public string? PortName { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DataDirection? Direction { get; set; }
        public string? DataPattern { get; set; }
        public bool CaseSensitive { get; set; } = false;
        public bool UseRegex { get; set; } = false;
        public bool IsActive { get; set; } = true;
    }

    public class SearchResult
    {
        public SerialDataFrame Frame { get; set; } = new();
        public int MatchIndex { get; set; }
        public string MatchText { get; set; } = string.Empty;
    }

    public interface IDataFilterService
    {
        Task<IReadOnlyList<SerialDataFrame>> ApplyFilterAsync(IEnumerable<SerialDataFrame> data, FilterCriteria criteria, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<SearchResult>> SearchAsync(IEnumerable<SerialDataFrame> data, string keyword, bool useRegex = false, bool caseSensitive = false, CancellationToken cancellationToken = default);
        Task SaveFilterPresetAsync(FilterCriteria criteria, string name, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<FilterCriteria>> LoadFilterPresetsAsync(CancellationToken cancellationToken = default);
        Task DeleteFilterPresetAsync(string name, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<string>> GetPresetNamesAsync(CancellationToken cancellationToken = default);
    }
}