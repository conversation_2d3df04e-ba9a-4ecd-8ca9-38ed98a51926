using System.Text.RegularExpressions;
using System.Text.Json;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.Infrastructure.Services
{
    public class DataFilterService : IDataFilterService, IDisposable
    {
        private readonly ILogger<DataFilterService> _logger;
        private readonly string _filterPresetsPath;
        private bool _disposed;

        public DataFilterService(ILogger<DataFilterService> logger)
        {
            _logger = logger;
            _filterPresetsPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "COMMonitor",
                "filter_presets.json");
        }

        public async Task<IReadOnlyList<SerialDataFrame>> ApplyFilterAsync(IEnumerable<SerialDataFrame> data, FilterCriteria criteria, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (criteria == null || !criteria.IsActive)
                return data.ToList();

            return await Task.Run(() =>
            {
                var query = data.AsQueryable();

                if (!string.IsNullOrEmpty(criteria.PortName))
                {
                    query = query.Where(f => f.PortName.Equals(criteria.PortName, StringComparison.OrdinalIgnoreCase));
                }

                if (criteria.StartTime.HasValue)
                {
                    query = query.Where(f => f.Timestamp >= criteria.StartTime.Value);
                }

                if (criteria.EndTime.HasValue)
                {
                    query = query.Where(f => f.Timestamp <= criteria.EndTime.Value);
                }

                if (criteria.Direction.HasValue)
                {
                    query = query.Where(f => f.Direction == criteria.Direction.Value);
                }

                if (!string.IsNullOrEmpty(criteria.DataPattern))
                {
                    if (criteria.UseRegex)
                    {
                        // Validate regex pattern to prevent regex injection
                        try
                        {
                            var options = criteria.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                            var regex = new Regex(criteria.DataPattern, options, TimeSpan.FromSeconds(5)); // Timeout to prevent DoS
                            query = query.Where(f => regex.IsMatch(f.DataAsText));
                        }
                        catch (ArgumentException)
                        {
                            // Invalid regex pattern, treat as literal text
                            _logger.LogWarning("Invalid regex pattern: {Pattern}", criteria.DataPattern);
                            query = query.Where(f => f.DataAsText.Contains(criteria.DataPattern, 
                                criteria.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase));
                        }
                    }
                    else
                    {
                        query = query.Where(f => f.DataAsText.Contains(criteria.DataPattern, 
                            criteria.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase));
                    }
                }

                return query.ToList();
            }, cancellationToken).ConfigureAwait(false);
        }

        public async Task<IReadOnlyList<SearchResult>> SearchAsync(IEnumerable<SerialDataFrame> data, string keyword, bool useRegex = false, bool caseSensitive = false, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (string.IsNullOrEmpty(keyword))
                return Array.Empty<SearchResult>();

            return await Task.Run(() =>
            {
                var results = new List<SearchResult>();
                var searchOptions = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

                foreach (var frame in data)
                {
                    var text = frame.DataAsText;
                    var hex = frame.DataAsHex;

                    if (useRegex)
                    {
                        var options = caseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                        
                        // Validate regex pattern to prevent regex injection
                        Regex? textRegex = null;
                        Regex? hexRegex = null;
                        
                        try
                        {
                            textRegex = new Regex(keyword, options, TimeSpan.FromSeconds(5)); // Timeout to prevent DoS
                            hexRegex = new Regex(keyword, options, TimeSpan.FromSeconds(5));
                        }
                        catch (ArgumentException)
                        {
                            _logger.LogWarning("Invalid regex pattern in search: {Pattern}", keyword);
                            // Fall back to literal search
                            useRegex = false;
                        }

                        if (textRegex != null && hexRegex != null)
                        {
                            var textMatches = textRegex.Matches(text);
                            var hexMatches = hexRegex.Matches(hex);

                            foreach (Match match in textMatches)
                            {
                                results.Add(new SearchResult
                                {
                                    Frame = frame,
                                    MatchIndex = match.Index,
                                    MatchText = match.Value
                                });
                            }

                            foreach (Match match in hexMatches)
                            {
                                results.Add(new SearchResult
                                {
                                    Frame = frame,
                                    MatchIndex = match.Index,
                                    MatchText = match.Value
                                });
                            }
                        }
                    }
                    else
                    {
                        var textIndex = text.IndexOf(keyword, searchOptions);
                        while (textIndex != -1)
                        {
                            results.Add(new SearchResult
                            {
                                Frame = frame,
                                MatchIndex = textIndex,
                                MatchText = keyword
                            });
                            textIndex = text.IndexOf(keyword, textIndex + 1, searchOptions);
                        }

                        var hexIndex = hex.IndexOf(keyword, searchOptions);
                        while (hexIndex != -1)
                        {
                            results.Add(new SearchResult
                            {
                                Frame = frame,
                                MatchIndex = hexIndex,
                                MatchText = keyword
                            });
                            hexIndex = hex.IndexOf(keyword, hexIndex + 1, searchOptions);
                        }
                    }
                }

                return results.OrderBy(r => r.Frame.Timestamp).ToList();
            }, cancellationToken).ConfigureAwait(false);
        }

        public async Task SaveFilterPresetAsync(FilterCriteria criteria, string name, CancellationToken cancellationToken = default)
        {
            if (criteria == null)
                throw new ArgumentNullException(nameof(criteria));

            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Preset name cannot be empty", nameof(name));

            try
            {
                var presets = await LoadFilterPresetsInternalAsync(cancellationToken).ConfigureAwait(false);

                var existingIndex = presets.FindIndex(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
                if (existingIndex >= 0)
                {
                    presets[existingIndex] = new FilterPreset { Name = name, Criteria = criteria };
                }
                else
                {
                    presets.Add(new FilterPreset { Name = name, Criteria = criteria });
                }

                await SaveFilterPresetsAsync(presets, cancellationToken).ConfigureAwait(false);
                _logger.LogInformation("Filter preset saved: {Name}", name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving filter preset: {Name}", name);
                throw;
            }
        }

        public async Task<IReadOnlyList<FilterCriteria>> LoadFilterPresetsAsync(CancellationToken cancellationToken = default)
        {
            var presets = await LoadFilterPresetsInternalAsync(cancellationToken).ConfigureAwait(false);
            return presets.Select(p => p.Criteria).ToList();
        }

        public async Task DeleteFilterPresetAsync(string name, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Preset name cannot be empty", nameof(name));

            try
            {
                var presets = await LoadFilterPresetsInternalAsync(cancellationToken).ConfigureAwait(false);
                var removed = presets.RemoveAll(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
                
                if (removed > 0)
                {
                    await SaveFilterPresetsAsync(presets, cancellationToken).ConfigureAwait(false);
                    _logger.LogInformation("Filter preset deleted: {Name}", name);
                }
                else
                {
                    _logger.LogWarning("Attempted to delete non-existent filter preset: {Name}", name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting filter preset: {Name}", name);
                throw;
            }
        }

        public async Task<IReadOnlyList<string>> GetPresetNamesAsync(CancellationToken cancellationToken = default)
        {
            var presets = await LoadFilterPresetsInternalAsync(cancellationToken).ConfigureAwait(false);
            return presets.Select(p => p.Name).ToList();
        }

        private async Task<List<FilterPreset>> LoadFilterPresetsInternalAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(_filterPresetsPath))
                    return new List<FilterPreset>();

                var json = await File.ReadAllTextAsync(_filterPresetsPath, cancellationToken).ConfigureAwait(false);
                var presets = System.Text.Json.JsonSerializer.Deserialize<List<FilterPreset>>(json);

                return presets ?? new List<FilterPreset>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading filter presets");
                return new List<FilterPreset>();
            }
        }

        private async Task SaveFilterPresetsAsync(IEnumerable<FilterPreset> presets, CancellationToken cancellationToken)
        {
            try
            {
                var directory = Path.GetDirectoryName(_filterPresetsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = System.Text.Json.JsonSerializer.Serialize(presets, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_filterPresetsPath, json, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving filter presets");
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // DataFilterService has no disposable resources to clean up
                }
                _disposed = true;
            }
        }

        private class FilterPreset
        {
            public string Name { get; set; } = string.Empty;
            public FilterCriteria Criteria { get; set; } = new();
        }
    }
}