using System.IO.Ports;
using System.Threading.Channels;
using COMMonitor.Core.Exceptions;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.Core.Services
{
    public class SerialPortService : ISerialPortService, IDisposable
    {
        private readonly ILogger<SerialPortService> _logger;
        private readonly SerialPort _serialPort;
        private readonly Channel<byte[]> _dataChannel;
        private readonly CancellationTokenSource _cts;
        private readonly object _lock = new();
        private Task? _readTask;
        private long _sequenceNumber;

        public event EventHandler<DataReceivedEventArgs>? DataReceived;
        public event EventHandler<SerialErrorEventArgs>? ErrorOccurred;

        public bool IsOpen => _serialPort?.IsOpen ?? false;
        public string PortName => _serialPort?.PortName ?? string.Empty;
        public SerialPortConfig CurrentConfig { get; private set; } = new();

        public SerialPortService(ILogger<SerialPortService> logger)
        {
            _logger = logger;
            _serialPort = new SerialPort();
            _dataChannel = Channel.CreateUnbounded<byte[]>(new UnboundedChannelOptions
            {
                SingleReader = true,
                SingleWriter = true
            });
            _cts = new CancellationTokenSource();
            _sequenceNumber = 0;

            ConfigureSerialPort();
        }

        public async Task<bool> OpenAsync(SerialPortConfig config, CancellationToken cancellationToken = default)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            if (!config.IsValid())
                throw new SerialPortException(SerialPortErrorCode.InvalidConfiguration, "Invalid serial port configuration");

            try
            {
                lock (_lock)
                {
                    if (_serialPort.IsOpen)
                    {
                        _logger.LogWarning("Attempted to open already open port: {PortName}", config.PortName);
                        return false;
                    }

                    _serialPort.PortName = config.PortName;
                    _serialPort.BaudRate = config.BaudRate;
                    _serialPort.DataBits = config.DataBits;
                    _serialPort.StopBits = config.StopBits;
                    _serialPort.Parity = config.Parity;
                    _serialPort.Handshake = config.Handshake;
                    _serialPort.ReadTimeout = config.ReadTimeout;
                    _serialPort.WriteTimeout = config.WriteTimeout;
                    _serialPort.DtrEnable = config.DtrEnable;
                    _serialPort.RtsEnable = config.RtsEnable;

                    _serialPort.Open();
                    CurrentConfig = config;
                }

                _logger.LogInformation("Serial port opened: {PortName} at {BaudRate} bps", config.PortName, config.BaudRate);

                // Start reading data
                _readTask = Task.Run(() => ReadDataAsync(_cts.Token), cancellationToken);

                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.AccessDenied, $"Access denied to port {config.PortName}", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.AccessDenied, error.Message));
                _logger.LogError(ex, "Access denied to serial port: {PortName}", config.PortName);
                return false;
            }
            catch (IOException ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.PortNotFound, $"Port {config.PortName} not found", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.PortNotFound, error.Message));
                _logger.LogError(ex, "Serial port not found: {PortName}", config.PortName);
                return false;
            }
            catch (InvalidOperationException ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.PortInUse, $"Port {config.PortName} is already in use", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.PortInUse, error.Message));
                _logger.LogError(ex, "Serial port already in use: {PortName}", config.PortName);
                return false;
            }
            catch (Exception ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.InvalidConfiguration, $"Failed to open port {config.PortName}", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.InvalidConfiguration, error.Message));
                _logger.LogError(ex, "Failed to open serial port: {PortName}", config.PortName);
                return false;
            }
        }

        public async Task CloseAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _cts.Cancel();

                if (_readTask != null)
                {
                    await _readTask.ConfigureAwait(false);
                }

                lock (_lock)
                {
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                        _logger.LogInformation("Serial port closed: {PortName}", PortName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error closing serial port");
            }
        }

        public async Task SendDataAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (!IsOpen)
                throw new SerialPortException(SerialPortErrorCode.ConnectionLost, "Serial port is not open");

            try
            {
                await _serialPort.BaseStream.WriteAsync(data, 0, data.Length, cancellationToken).ConfigureAwait(false);

                var frame = new SerialDataFrame
                {
                    Timestamp = DateTime.Now,
                    Data = data,
                    Direction = DataDirection.Transmit,
                    PortName = PortName,
                    SequenceNumber = Interlocked.Increment(ref _sequenceNumber)
                };

                DataReceived?.Invoke(this, new DataReceivedEventArgs(frame));
                
                _logger.LogDebug("Sent {Bytes} bytes to {PortName}", data.Length, PortName);
            }
            catch (TimeoutException ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.Timeout, "Timeout sending data", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.Timeout, error.Message));
                throw error;
            }
            catch (Exception ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.SendFailed, "Failed to send data", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.SendFailed, error.Message));
                throw error;
            }
        }

        public async Task<IReadOnlyList<string>> GetAvailablePortsAsync(CancellationToken cancellationToken = default)
        {
            return await Task.Run(() => SerialPort.GetPortNames(), cancellationToken).ConfigureAwait(false);
        }

        private async Task ReadDataAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsOpen)
                {
                    try
                    {
                        var bytesRead = await _serialPort.BaseStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken).ConfigureAwait(false);
                        if (bytesRead > 0)
                        {
                            var data = new byte[bytesRead];
                            Array.Copy(buffer, data, bytesRead);

                            var frame = new SerialDataFrame
                            {
                                Timestamp = DateTime.Now,
                                Data = data,
                                Direction = DataDirection.Receive,
                                PortName = PortName,
                                SequenceNumber = Interlocked.Increment(ref _sequenceNumber)
                            };

                            DataReceived?.Invoke(this, new DataReceivedEventArgs(frame));
                            
                            _logger.LogDebug("Received {Bytes} bytes from {PortName}", bytesRead, PortName);
                        }
                    }
                    catch (TimeoutException)
                    {
                        // Normal timeout, continue reading
                        continue;
                    }
                    catch (OperationCanceledException)
                    {
                        // Cancellation requested, exit gracefully
                        break;
                    }
                    catch (IOException ex)
                    {
                        var error = new SerialPortException(SerialPortErrorCode.ConnectionLost, "Connection lost", ex);
                        ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.ConnectionLost, error.Message));
                        _logger.LogError(ex, "Connection lost");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new SerialPortException(SerialPortErrorCode.ReceiveFailed, "Error reading data", ex);
                ErrorOccurred?.Invoke(this, new SerialErrorEventArgs(SerialPortErrorCode.ReceiveFailed, error.Message));
                _logger.LogError(ex, "Error in read data loop");
            }
        }

        private void ConfigureSerialPort()
        {
            _serialPort.ReadTimeout = 100;
            _serialPort.WriteTimeout = 100;
            _serialPort.ReadBufferSize = 65536;
            _serialPort.WriteBufferSize = 65536;
            _serialPort.Encoding = System.Text.Encoding.ASCII;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _cts?.Dispose();
                _serialPort?.Dispose();
            }
        }
    }
}