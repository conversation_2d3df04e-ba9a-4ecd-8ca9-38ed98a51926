using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace COMMonitor.UI.Services
{
    public static class GlobalExceptionHandler
    {
        private static ILogger? _logger;

        public static void Register(ILogger logger)
        {
            _logger = logger;

            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            Application.ThreadException += OnThreadException;
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        }

        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            _logger?.LogCritical(exception, "Unhandled application exception");
            
            ShowErrorDialog("发生未处理的异常", exception?.Message ?? "未知错误", true);
            
            if (e.IsTerminating)
            {
                Application.Exit();
            }
        }

        private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            _logger?.LogError(e.Exception, "UI thread exception");
            ShowErrorDialog("UI线程异常", e.Exception.Message, false);
        }

        private static void ShowErrorDialog(string title, string message, bool isFatal)
        {
            var result = MessageBox.Show(
                $"{title}\n\n{message}\n\n{(isFatal ? "程序将关闭。" : "点击确定继续，或点击取消查看日志。")}",
                "COM Monitor - 错误",
                isFatal ? MessageBoxButtons.OK : MessageBoxButtons.OKCancel,
                MessageBoxIcon.Error);

            if (!isFatal && result == DialogResult.Cancel)
            {
                OpenLogDirectory();
            }
        }

        private static void OpenLogDirectory()
        {
            try
            {
                var logDirectory = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "COMMonitor", "logs");

                if (Directory.Exists(logDirectory))
                {
                    // 使用更安全的方式打开文件夹，避免直接调用进程
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = logDirectory,
                        UseShellExecute = true,
                        Verb = "open"
                    };
                    Process.Start(startInfo);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to open log directory");
            }
        }
    }
}