namespace COMMonitor.Core.Models
{
    public enum LogFormat
    {
        Text,
        Hex,
        Mixed,
        Json
    }

    public class LogFileConfig
    {
        public string LogDirectory { get; set; } = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "COMMonitor");

        public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100MB
        public int MaxFileCount { get; set; } = 100;
        public LogFormat Format { get; set; } = LogFormat.Text;
        public bool AutoRotate { get; set; } = true;
        public bool IncludeTimestamp { get; set; } = true;
        public bool IncludeDirection { get; set; } = true;

        public string GetLogFilePath()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var extension = Format switch
            {
                LogFormat.Json => ".json",
                _ => ".log"
            };
            return Path.Combine(LogDirectory, $"COMMonitor_{timestamp}{extension}");
        }

        public void EnsureDirectoryExists()
        {
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }
    }
}