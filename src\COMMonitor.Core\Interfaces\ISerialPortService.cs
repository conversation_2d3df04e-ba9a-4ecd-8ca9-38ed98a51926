using COMMonitor.Core.Exceptions;
using COMMonitor.Core.Models;

namespace COMMonitor.Core.Interfaces
{
    public class DataReceivedEventArgs : EventArgs
    {
        public SerialDataFrame DataFrame { get; }

        public DataReceivedEventArgs(SerialDataFrame dataFrame)
        {
            DataFrame = dataFrame;
        }
    }

    public class SerialErrorEventArgs : EventArgs
    {
        public SerialPortErrorCode ErrorCode { get; }
        public string Message { get; }

        public SerialErrorEventArgs(SerialPortErrorCode errorCode, string message)
        {
            ErrorCode = errorCode;
            Message = message;
        }
    }

    public interface ISerialPortService
    {
        Task<bool> OpenAsync(SerialPortConfig config, CancellationToken cancellationToken = default);
        Task CloseAsync(CancellationToken cancellationToken = default);
        Task SendDataAsync(byte[] data, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<string>> GetAvailablePortsAsync(CancellationToken cancellationToken = default);
        
        event EventHandler<DataReceivedEventArgs> DataReceived;
        event EventHandler<SerialErrorEventArgs> ErrorOccurred;
        
        bool IsOpen { get; }
        string PortName { get; }
        SerialPortConfig CurrentConfig { get; }
    }
}