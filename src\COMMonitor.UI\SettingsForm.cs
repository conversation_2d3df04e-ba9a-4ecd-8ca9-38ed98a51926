using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;

namespace COMMonitor.UI
{
    public partial class SettingsForm : Form
    {
        private readonly IConfigurationService _configService;
        private ApplicationConfig _config;

        public SettingsForm(IConfigurationService configService)
        {
            _configService = configService;
            InitializeComponent();
        }

        private async void SettingsForm_Load(object sender, EventArgs e)
        {
            _config = await _configService.LoadConfigurationAsync();
            LoadSettings();
        }

        private void LoadSettings()
        {
            // 这里实现设置界面的加载逻辑
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            SaveSettings();
            await _configService.SaveConfigurationAsync(_config);
            DialogResult = DialogResult.OK;
            Close();
        }

        private void SaveSettings()
        {
            // 这里实现设置保存逻辑
        }

    }
}