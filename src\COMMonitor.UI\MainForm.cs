using COMMonitor.Core.Models;
using COMMonitor.UI.ViewModels;
using Microsoft.Extensions.Logging;
using System.ComponentModel;

namespace COMMonitor.UI
{
    public partial class MainForm : Form
    {
        private readonly MainViewModel _viewModel;
        private readonly ILogger<MainForm> _logger;
        private readonly PortConfigForm _portConfigForm;
        private readonly FilterForm _filterForm;
        private readonly LogViewerForm _logViewerForm;
        private readonly SettingsForm _settingsForm;

        public MainForm(
            MainViewModel viewModel,
            PortConfigForm portConfigForm,
            FilterForm filterForm,
            LogViewerForm logViewerForm,
            SettingsForm settingsForm,
            ILogger<MainForm> logger)
        {
            InitializeComponent();
            
            _viewModel = viewModel;
            _portConfigForm = portConfigForm;
            _filterForm = filterForm;
            _logViewerForm = logViewerForm;
            _settingsForm = settingsForm;
            _logger = logger;

            InitializeDataBindings();
            InitializeEventHandlers();
            SetupDataGridView();
            SetupStatusBar();
        }

        private void InitializeDataBindings()
        {
            // Port selection
            cmbPort.DataSource = _viewModel.AvailablePorts;
            cmbPort.DataBindings.Add("SelectedItem", _viewModel, "CurrentConfig.PortName", false, DataSourceUpdateMode.OnPropertyChanged);

            // Baud rate
            cmbBaudRate.DataSource = new[] { 300, 600, 1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600 };
            cmbBaudRate.DataBindings.Add("SelectedItem", _viewModel, "CurrentConfig.BaudRate", false, DataSourceUpdateMode.OnPropertyChanged);

            // Data bits
            cmbDataBits.DataSource = new[] { 5, 6, 7, 8 };
            cmbDataBits.DataBindings.Add("SelectedItem", _viewModel, "CurrentConfig.DataBits", false, DataSourceUpdateMode.OnPropertyChanged);

            // Parity
            cmbParity.DataSource = Enum.GetValues(typeof(System.IO.Ports.Parity));
            cmbParity.DataBindings.Add("SelectedItem", _viewModel, "CurrentConfig.Parity", false, DataSourceUpdateMode.OnPropertyChanged);

            // Stop bits
            cmbStopBits.DataSource = Enum.GetValues(typeof(System.IO.Ports.StopBits));
            cmbStopBits.DataBindings.Add("SelectedItem", _viewModel, "CurrentConfig.StopBits", false, DataSourceUpdateMode.OnPropertyChanged);

            // Display mode
            cmbDisplayMode.DataSource = Enum.GetValues(typeof(DisplayMode));
            cmbDisplayMode.DataBindings.Add("SelectedItem", _viewModel, "DisplayMode", false, DataSourceUpdateMode.OnPropertyChanged);
        }

        private void InitializeEventHandlers()
        {
            _viewModel.PropertyChanged += OnViewModelPropertyChanged;
            _viewModel.DataFrames.CollectionChanged += OnDataFramesCollectionChanged;

            // Form events
            FormClosing += OnFormClosing;
            Shown += OnFormShown;

            // Button events
            btnConnect.Click += async (s, e) => await _viewModel.ConnectAsync();
            btnDisconnect.Click += async (s, e) => await _viewModel.DisconnectAsync();
            btnStartMonitoring.Click += async (s, e) => await _viewModel.StartMonitoringAsync();
            btnStopMonitoring.Click += async (s, e) => await _viewModel.StopMonitoringAsync();
            btnClear.Click += (s, e) => _viewModel.ClearDisplay();
            btnRefreshPorts.Click += async (s, e) => await _viewModel.RefreshAvailablePortsAsync();

            // Menu events
            miFileExit.Click += (s, e) => Close();
            miViewPortConfig.Click += (s, e) => _portConfigForm.ShowDialog(this);
            miViewFilter.Click += (s, e) => _filterForm.ShowDialog(this);
            miViewLogViewer.Click += (s, e) => _logViewerForm.ShowDialog(this);
            miToolsSettings.Click += (s, e) => _settingsForm.ShowDialog(this);
            miHelpAbout.Click += (s, e) => ShowAboutDialog();

            // Search
            btnSearch.Click += async (s, e) => await PerformSearch();
            txtSearch.KeyDown += async (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    await PerformSearch();
                    e.SuppressKeyPress = true;
                }
            };
        }

        private void SetupDataGridView()
        {
            dgvData.AutoGenerateColumns = false;
            dgvData.AllowUserToAddRows = false;
            dgvData.AllowUserToDeleteRows = false;
            dgvData.ReadOnly = true;
            dgvData.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvData.MultiSelect = false;

            // Columns
            dgvData.Columns.Clear();

            dgvData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "时间戳",
                DataPropertyName = "Timestamp",
                DefaultCellStyle = { Format = "HH:mm:ss.fff" },
                Width = 100
            });

            dgvData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PortName",
                HeaderText = "端口",
                DataPropertyName = "PortName",
                Width = 60
            });

            dgvData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Direction",
                HeaderText = "方向",
                DataPropertyName = "Direction",
                Width = 50
            });

            dgvData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Length",
                HeaderText = "长度",
                DataPropertyName = "Length",
                Width = 50
            });

            dgvData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Data",
                HeaderText = "数据",
                DataPropertyName = "Data",
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            // Context menu
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("复制", null, (s, e) => CopySelectedData());
            contextMenu.Items.Add("复制十六进制", null, (s, e) => CopySelectedHex());
            contextMenu.Items.Add("复制文本", null, (s, e) => CopySelectedText());
            contextMenu.Items.Add(new ToolStripSeparator());
            contextMenu.Items.Add("清除所有", null, (s, e) => _viewModel.ClearDisplay());
            dgvData.ContextMenuStrip = contextMenu;
        }

        private void SetupStatusBar()
        {
            statusStrip.LayoutStyle = ToolStripLayoutStyle.HorizontalStackWithOverflow;
            statusStrip.Items.Add(lblStatus);
            statusStrip.Items.Add(new ToolStripSeparator());
            statusStrip.Items.Add(lblBytesReceived);
            statusStrip.Items.Add(new ToolStripSeparator());
            statusStrip.Items.Add(lblBytesSent);
            statusStrip.Items.Add(new ToolStripSeparator());
            statusStrip.Items.Add(lblErrorCount);
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_viewModel.DisplayMode))
            {
                UpdateDataDisplay();
            }
        }

        private void OnDataFramesCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action(() => OnDataFramesCollectionChanged(sender, e)));
                return;
            }

            UpdateDataDisplay();
        }

        private void UpdateDataDisplay()
        {
            if (dgvData.Rows.Count != _viewModel.DataFrames.Count)
            {
                dgvData.DataSource = null;
                dgvData.DataSource = _viewModel.DataFrames;
            }

            // Auto-scroll to bottom
            if (dgvData.Rows.Count > 0)
            {
                dgvData.FirstDisplayedScrollingRowIndex = dgvData.Rows.Count - 1;
            }
        }

        private async Task PerformSearch()
        {
            var keyword = txtSearch.Text.Trim();
            if (string.IsNullOrEmpty(keyword))
                return;

            try
            {
                var results = await _viewModel.SearchAsync(keyword, chkUseRegex.Checked, chkCaseSensitive.Checked);
                if (results.Count > 0)
                {
                    // Highlight and scroll to first match
                    var firstResult = results[0];
                    var index = _viewModel.DataFrames.IndexOf(firstResult.Frame);
                    if (index >= 0)
                    {
                        dgvData.ClearSelection();
                        dgvData.Rows[index].Selected = true;
                        dgvData.FirstDisplayedScrollingRowIndex = index;
                    }
                }
                else
                {
                    MessageBox.Show("未找到匹配项", "搜索", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search");
                MessageBox.Show($"搜索错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CopySelectedData()
        {
            if (dgvData.SelectedRows.Count == 0) return;

            var row = dgvData.SelectedRows[0];
            var frame = (SerialDataFrame)row.DataBoundItem;
            Clipboard.SetText(frame.ToString());
        }

        private void CopySelectedHex()
        {
            if (dgvData.SelectedRows.Count == 0) return;

            var row = dgvData.SelectedRows[0];
            var frame = (SerialDataFrame)row.DataBoundItem;
            Clipboard.SetText(frame.DataAsHex);
        }

        private void CopySelectedText()
        {
            if (dgvData.SelectedRows.Count == 0) return;

            var row = dgvData.SelectedRows[0];
            var frame = (SerialDataFrame)row.DataBoundItem;
            Clipboard.SetText(frame.DataAsText);
        }

        private void ShowAboutDialog()
        {
            var aboutForm = new Form
            {
                Text = "关于 COM Monitor",
                Width = 300,
                Height = 200,
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            panel.Controls.AddRange(new Control[]
            {
                new Label
                {
                    Text = "COM Monitor v1.0.0",
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    Location = new Point(20, 20),
                    AutoSize = true
                },
                new Label
                {
                    Text = "串口通信监控工具",
                    Location = new Point(20, 50),
                    AutoSize = true
                },
                new Label
                {
                    Text = "© 2024 COM Monitor Team",
                    Location = new Point(20, 100),
                    AutoSize = true
                },
                new Button
                {
                    Text = "确定",
                    Location = new Point(200, 130),
                    Width = 80,
                    DialogResult = DialogResult.OK
                }
            });

            aboutForm.Controls.Add(panel);
            aboutForm.ShowDialog(this);
        }

        private async void OnFormClosing(object? sender, FormClosingEventArgs e)
        {
            if (_viewModel.IsMonitoring)
            {
                var result = MessageBox.Show(
                    "正在监控中，是否停止监控并退出？",
                    "确认退出",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    e.Cancel = true;
                    return;
                }

                await _viewModel.StopMonitoringAsync();
            }

            await _viewModel.SaveConfigurationAsync();
        }

        private async void OnFormShown(object? sender, EventArgs e)
        {
            await _viewModel.RefreshAvailablePortsAsync();
        }
    }
}