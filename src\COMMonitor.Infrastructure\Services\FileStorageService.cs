using System.IO.Compression;
using System.Text;
using System.Text.Json;
using COMMonitor.Core.Exceptions;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.Infrastructure.Services
{
    /// <summary>
    /// 文件存储服务 - 用于合法的串口数据日志记录
    /// 仅用于保存串口通信数据以供调试和分析使用
    /// File Storage Service - For legitimate serial port data logging
    /// Only used to save serial communication data for debugging and analysis purposes
    /// </summary>
    public class FileStorageService : IDataStorageService, IDisposable
    {
        private readonly ILogger<FileStorageService> _logger;
        private readonly SemaphoreSlim _fileLock = new(1, 1);
        private readonly CancellationTokenSource _cts = new();
        private StreamWriter? _currentWriter;
        private LogFileConfig? _currentConfig;
        private string? _currentLogFile;
        private long _bytesWritten;
        private long _currentFileSize;
        private DateTime _lastWriteTime;

        public bool IsLogging { get; private set; }
        public string CurrentLogFile { get; private set; } = string.Empty;
        public long BytesWritten { get; private set; }

        public FileStorageService(ILogger<FileStorageService> logger)
        {
            _logger = logger;
            _bytesWritten = 0;
            _currentFileSize = 0;
            _lastWriteTime = DateTime.Now;
        }

        public async Task StartLoggingAsync(LogFileConfig config, CancellationToken cancellationToken = default)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            await _fileLock.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                if (IsLogging)
                {
                    await StopLoggingAsync(cancellationToken).ConfigureAwait(false);
                }

                _currentConfig = config;
                config.EnsureDirectoryExists();

                _currentLogFile = config.GetLogFilePath();
                var directory = Path.GetDirectoryName(_currentLogFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var fileStream = new FileStream(_currentLogFile, FileMode.Append, FileAccess.Write, FileShare.Read, 4096, true);
                _currentWriter = new StreamWriter(fileStream, Encoding.UTF8);

                if (fileStream.Position == 0)
                {
                    await WriteHeaderAsync(cancellationToken).ConfigureAwait(false);
                }

                IsLogging = true;
                CurrentLogFile = _currentLogFile;
                _currentFileSize = fileStream.Position;
                _lastWriteTime = DateTime.Now;

                _logger.LogInformation("Started logging to file: {LogFile}", _currentLogFile);

                _ = Task.Run(async () => await MonitorLogFileAsync(_cts.Token).ConfigureAwait(false), _cts.Token);
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task StopLoggingAsync(CancellationToken cancellationToken = default)
        {
            await _fileLock.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                if (!IsLogging || _currentWriter == null)
                    return;

                try
                {
                    await _currentWriter.FlushAsync().ConfigureAwait(false);
                    await _currentWriter.DisposeAsync().ConfigureAwait(false);
                    _currentWriter = null;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error closing log file");
                }

                IsLogging = false;
                _currentLogFile = null;
                _currentConfig = null;
                _currentFileSize = 0;

                _logger.LogInformation("Stopped logging");
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task LogDataAsync(SerialDataFrame frame, CancellationToken cancellationToken = default)
        {
            if (!IsLogging || _currentWriter == null || _currentConfig == null)
                return;

            await _fileLock.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                var logLine = FormatLogLine(frame, _currentConfig.Format);
                await _currentWriter.WriteLineAsync(logLine).ConfigureAwait(false);
                
                _currentFileSize += Encoding.UTF8.GetByteCount(logLine + Environment.NewLine);
                BytesWritten += Encoding.UTF8.GetByteCount(logLine + Environment.NewLine);
                _lastWriteTime = DateTime.Now;

                if (_currentFileSize >= _currentConfig.MaxFileSize && _currentConfig.AutoRotate)
                {
                    await RotateLogFileAsync(cancellationToken).ConfigureAwait(false);
                }
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<IReadOnlyList<string>> GetLogFilesAsync(CancellationToken cancellationToken = default)
        {
            if (_currentConfig == null)
                return Array.Empty<string>();

            try
            {
                var directory = new DirectoryInfo(_currentConfig.LogDirectory);
                if (!directory.Exists)
                    return Array.Empty<string>();

                var files = await Task.Run(() =>
                {
                    return directory.GetFiles("COMMonitor_*.log")
                        .Concat(directory.GetFiles("COMMonitor_*.json"))
                        .OrderByDescending(f => f.LastWriteTime)
                        .Select(f => f.FullName)
                        .ToList();
                }, cancellationToken).ConfigureAwait(false);

                return files;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting log files");
                return Array.Empty<string>();
            }
        }

        public async Task<Stream> OpenLogFileAsync(string fileName, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("File name cannot be empty", nameof(fileName));

            // Validate file path to prevent directory traversal
            if (_currentConfig == null)
                throw new InvalidOperationException("Logging not initialized");

            var fullPath = Path.GetFullPath(fileName);
            var logDirectory = Path.GetFullPath(_currentConfig.LogDirectory);
            
            if (!fullPath.StartsWith(logDirectory, StringComparison.OrdinalIgnoreCase))
                throw new UnauthorizedAccessException("Access denied to specified file path");

            if (!File.Exists(fullPath))
                throw new FileNotFoundException("Log file not found", fullPath);

            try
            {
                var fileStream = new FileStream(fullPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite, 4096, true);
                return fileStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening log file: {FileName}", fileName);
                throw new DataStorageException($"Failed to open log file: {fileName}", ex);
            }
        }

        public async Task DeleteLogFileAsync(string fileName, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("File name cannot be empty", nameof(fileName));

            // Validate file path to prevent directory traversal
            if (_currentConfig == null)
                throw new InvalidOperationException("Logging not initialized");

            var fullPath = Path.GetFullPath(fileName);
            var logDirectory = Path.GetFullPath(_currentConfig.LogDirectory);
            
            if (!fullPath.StartsWith(logDirectory, StringComparison.OrdinalIgnoreCase))
                throw new UnauthorizedAccessException("Access denied to specified file path");

            if (!File.Exists(fullPath))
                throw new FileNotFoundException("Log file not found", fullPath);

            try
            {
                if (string.Equals(fullPath, _currentLogFile, StringComparison.OrdinalIgnoreCase))
                {
                    throw new DataStorageException("Cannot delete current log file");
                }

                await Task.Run(() => File.Delete(fullPath), cancellationToken).ConfigureAwait(false);
                _logger.LogInformation("Deleted log file: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting log file: {FileName}", fileName);
                throw new DataStorageException($"Failed to delete log file: {fileName}", ex);
            }
        }

        private string FormatLogLine(SerialDataFrame frame, LogFormat format)
        {
            return format switch
            {
                LogFormat.Json => FormatJson(frame),
                LogFormat.Hex => FormatHex(frame),
                LogFormat.Text => FormatText(frame),
                LogFormat.Mixed => FormatMixed(frame),
                _ => FormatMixed(frame)
            };
        }

        private string FormatJson(SerialDataFrame frame)
        {
            var logEntry = new
            {
                Timestamp = frame.Timestamp,
                PortName = frame.PortName,
                Direction = frame.Direction.ToString(),
                Length = frame.Length,
                Data = Convert.ToBase64String(frame.Data),
                Text = frame.DataAsText
            };

            return JsonSerializer.Serialize(logEntry, new JsonSerializerOptions { WriteIndented = false });
        }

        private string FormatHex(SerialDataFrame frame)
        {
            var timestamp = _currentConfig?.IncludeTimestamp == true ? $"[{frame.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] " : "";
            var direction = _currentConfig?.IncludeDirection == true ? $"[{frame.Direction}] " : "";
            return $"{timestamp}{direction}{frame.DataAsHex}";
        }

        private string FormatText(SerialDataFrame frame)
        {
            var timestamp = _currentConfig?.IncludeTimestamp == true ? $"[{frame.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] " : "";
            var direction = _currentConfig?.IncludeDirection == true ? $"[{frame.Direction}] " : "";
            return $"{timestamp}{direction}{EscapeText(frame.DataAsText)}";
        }

        private string FormatMixed(SerialDataFrame frame)
        {
            var timestamp = _currentConfig?.IncludeTimestamp == true ? $"[{frame.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] " : "";
            var direction = _currentConfig?.IncludeDirection == true ? $"[{frame.Direction}] " : "";
            var hexData = frame.DataAsHex;
            var textData = EscapeText(frame.DataAsText);
            return $"{timestamp}{direction}[{frame.Length:000}] {hexData} | {textData}";
        }

        private string EscapeText(string text)
        {
            return text.Replace("\r", "\\r")
                      .Replace("\n", "\\n")
                      .Replace("\t", "\\t")
                      .Replace("\0", "\\0");
        }

        private async Task WriteHeaderAsync(CancellationToken cancellationToken)
        {
            if (_currentWriter == null || _currentConfig == null)
                return;

            var header = new StringBuilder();
            header.AppendLine($"# COM Monitor Log");
            header.AppendLine($"# Start Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            header.AppendLine($"# Format: {_currentConfig.Format}");
            header.AppendLine($"# Port: {Environment.MachineName}");
            header.AppendLine("#");

            await _currentWriter.WriteAsync(header.ToString());
        }

        private async Task RotateLogFileAsync(CancellationToken cancellationToken)
        {
            if (_currentConfig == null)
                return;

            await StopLoggingAsync(cancellationToken);
            await CleanOldLogFilesAsync(cancellationToken);
            await StartLoggingAsync(_currentConfig, cancellationToken);
        }

        private async Task CleanOldLogFilesAsync(CancellationToken cancellationToken)
        {
            if (_currentConfig == null)
                return;

            try
            {
                var directory = new DirectoryInfo(_currentConfig.LogDirectory);
                if (!directory.Exists)
                    return;

                var files = directory.GetFiles("COMMonitor_*.log")
                    .Concat(directory.GetFiles("COMMonitor_*.json"))
                    .OrderByDescending(f => f.LastWriteTime)
                    .Skip(_currentConfig.MaxFileCount)
                    .ToList();

                foreach (var file in files)
                {
                    try
                    {
                        file.Delete();
                        _logger.LogInformation("Deleted old log file: {FileName}", file.Name);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting old log file: {FileName}", file.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning old log files");
            }
        }

        private async Task MonitorLogFileAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken);

                    if (IsLogging && _currentWriter != null)
                    {
                        await _currentWriter.FlushAsync();
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in log file monitoring");
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _cts?.Cancel();
                _cts?.Dispose();
                _fileLock?.Dispose();
                _currentWriter?.Dispose();
            }
        }
    }
}