using COMMonitor.Core.Models;

namespace COMMonitor.Core.Interfaces
{
    public interface IConfigurationService
    {
        Task<ApplicationConfig> LoadConfigurationAsync(CancellationToken cancellationToken = default);
        Task SaveConfigurationAsync(ApplicationConfig config, CancellationToken cancellationToken = default);
        Task<bool> ResetConfigurationAsync(CancellationToken cancellationToken = default);
        Task<ApplicationConfig> ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default);
        Task ExportConfigurationAsync(ApplicationConfig config, string filePath, CancellationToken cancellationToken = default);
        
        string ConfigurationFilePath { get; }
        bool ConfigurationExists { get; }
    }
}