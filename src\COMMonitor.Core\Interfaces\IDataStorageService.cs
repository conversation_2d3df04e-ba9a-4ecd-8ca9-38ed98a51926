using COMMonitor.Core.Models;

namespace COMMonitor.Core.Interfaces
{
    public interface IDataStorageService
    {
        Task StartLoggingAsync(LogFileConfig config, CancellationToken cancellationToken = default);
        Task StopLoggingAsync(CancellationToken cancellationToken = default);
        Task LogDataAsync(SerialDataFrame frame, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<string>> GetLogFilesAsync(CancellationToken cancellationToken = default);
        Task<Stream> OpenLogFileAsync(string fileName, CancellationToken cancellationToken = default);
        Task DeleteLogFileAsync(string fileName, CancellationToken cancellationToken = default);
        
        bool IsLogging { get; }
        string CurrentLogFile { get; }
        long BytesWritten { get; }
    }
}