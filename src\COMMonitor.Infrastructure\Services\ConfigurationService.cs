using System.Text.Json;
using COMMonitor.Core.Exceptions;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.Infrastructure.Services
{
    public class ConfigurationService : IConfigurationService, IDisposable
    {
        private bool _disposed;
        private readonly ILogger<ConfigurationService> _logger;
        private readonly string _configDirectory;
        private readonly string _configFileName;
        private readonly JsonSerializerOptions _jsonOptions;

        public string ConfigurationFilePath { get; }
        public bool ConfigurationExists => File.Exists(ConfigurationFilePath);

        public ConfigurationService(ILogger<ConfigurationService> logger)
        {
            _logger = logger;
            _configDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "COMMonitor");
            _configFileName = "config.json";
            ConfigurationFilePath = Path.Combine(_configDirectory, _configFileName);

            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
            };

            EnsureDirectoryExists();
        }

        public async Task<ApplicationConfig> LoadConfigurationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ConfigurationExists)
                {
                    _logger.LogInformation("Configuration file not found, creating default configuration");
                    var defaultConfig = CreateDefaultConfiguration();
                    await SaveConfigurationAsync(defaultConfig, cancellationToken);
                    return defaultConfig;
                }

                var json = await File.ReadAllTextAsync(ConfigurationFilePath, cancellationToken);
                var config = JsonSerializer.Deserialize<ApplicationConfig>(json, _jsonOptions);

                if (config == null)
                {
                    throw new ConfigurationException("Failed to deserialize configuration file");
                }

                ValidateConfiguration(config);
                _logger.LogInformation("Configuration loaded successfully");
                return config;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing configuration file");
                var backupPath = await CreateBackupAsync();
                _logger.LogWarning("Configuration file corrupted. Backup created at: {BackupPath}", backupPath);
                
                var defaultConfig = CreateDefaultConfiguration();
                await SaveConfigurationAsync(defaultConfig, cancellationToken);
                return defaultConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading configuration");
                throw new ConfigurationException("Failed to load configuration", ex);
            }
        }

        public async Task SaveConfigurationAsync(ApplicationConfig config, CancellationToken cancellationToken = default)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            try
            {
                ValidateConfiguration(config);

                var json = JsonSerializer.Serialize(config, _jsonOptions);
                await File.WriteAllTextAsync(ConfigurationFilePath, json, cancellationToken);

                _logger.LogInformation("Configuration saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving configuration");
                throw new ConfigurationException("Failed to save configuration", ex);
            }
        }

        public async Task<bool> ResetConfigurationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var defaultConfig = CreateDefaultConfiguration();
                await SaveConfigurationAsync(defaultConfig, cancellationToken);
                _logger.LogInformation("Configuration reset to defaults");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting configuration");
                return false;
            }
        }

        public async Task<ApplicationConfig> ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException("Configuration file not found", filePath);

            try
            {
                var json = await File.ReadAllTextAsync(filePath, cancellationToken);
                var config = JsonSerializer.Deserialize<ApplicationConfig>(json, _jsonOptions);

                if (config == null)
                    throw new ConfigurationException("Invalid configuration file format");

                ValidateConfiguration(config);
                await SaveConfigurationAsync(config, cancellationToken);

                _logger.LogInformation("Configuration imported from: {FilePath}", filePath);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing configuration from: {FilePath}", filePath);
                throw new ConfigurationException($"Failed to import configuration from: {filePath}", ex);
            }
        }

        public async Task ExportConfigurationAsync(ApplicationConfig config, string filePath, CancellationToken cancellationToken = default)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            try
            {
                ValidateConfiguration(config);
                var directory = Path.GetDirectoryName(filePath);

                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(config, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json, cancellationToken);

                _logger.LogInformation("Configuration exported to: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting configuration to: {FilePath}", filePath);
                throw new ConfigurationException($"Failed to export configuration to: {filePath}", ex);
            }
        }

        private ApplicationConfig CreateDefaultConfiguration()
        {
            return new ApplicationConfig
            {
                DefaultSerialConfig = new SerialPortConfig
                {
                    PortName = "COM1",
                    BaudRate = 9600,
                    DataBits = 8,
                    StopBits = System.IO.Ports.StopBits.One,
                    Parity = System.IO.Ports.Parity.None,
                    Handshake = System.IO.Ports.Handshake.None
                },
                LogConfig = new LogFileConfig(),
                DisplayConfig = new DisplayConfig(),
                WindowConfig = new WindowConfig(),
                Language = "zh-CN",
                CheckForUpdates = true,
                MinimizeToTray = false
            };
        }

        private void ValidateConfiguration(ApplicationConfig config)
        {
            if (config.DefaultSerialConfig == null)
                throw new ConfigurationException("Default serial configuration is required");

            if (config.LogConfig == null)
                throw new ConfigurationException("Log configuration is required");

            if (config.DisplayConfig == null)
                throw new ConfigurationException("Display configuration is required");

            if (config.WindowConfig == null)
                throw new ConfigurationException("Window configuration is required");

            if (!config.DefaultSerialConfig.IsValid())
                throw new ConfigurationException("Invalid default serial configuration");

            if (config.LogConfig.MaxFileSize <= 0)
                throw new ConfigurationException("Max file size must be greater than 0");

            if (config.LogConfig.MaxFileCount <= 0)
                throw new ConfigurationException("Max file count must be greater than 0");

            if (config.DisplayConfig.MaxDisplayRows <= 0)
                throw new ConfigurationException("Max display rows must be greater than 0");

            if (config.DisplayConfig.FontSize <= 0)
                throw new ConfigurationException("Font size must be greater than 0");

            if (string.IsNullOrWhiteSpace(config.DisplayConfig.FontFamily))
                throw new ConfigurationException("Font family is required");
        }

        private async Task<string> CreateBackupAsync()
        {
            if (!ConfigurationExists)
                return string.Empty;

            var backupFileName = $"config_backup_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            var backupPath = Path.Combine(_configDirectory, backupFileName);

            try
            {
                File.Copy(ConfigurationFilePath, backupPath, true);
                return backupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating configuration backup");
                return string.Empty;
            }
        }

        private void EnsureDirectoryExists()
        {
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
                _logger.LogInformation("Created configuration directory: {Directory}", _configDirectory);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Configuration service has no disposable resources to clean up
                }
                _disposed = true;
            }
        }
    }
}