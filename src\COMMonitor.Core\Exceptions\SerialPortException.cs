namespace COMMonitor.Core.Exceptions
{
    public enum SerialPortErrorCode
    {
        PortNotFound,
        PortInUse,
        AccessDenied,
        InvalidConfiguration,
        Timeout,
        BufferOverflow,
        ConnectionLost,
        SendFailed,
        ReceiveFailed
    }

    public class SerialPortException : Exception
    {
        public SerialPortErrorCode ErrorCode { get; }

        public SerialPortException(SerialPortErrorCode errorCode, string message) 
            : base(message)
        {
            ErrorCode = errorCode;
        }

        public SerialPortException(SerialPortErrorCode errorCode, string message, Exception innerException) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
        }

        public override string ToString()
        {
            return $"[{ErrorCode}] {Message}";
        }
    }

    public class DataStorageException : Exception
    {
        public DataStorageException(string message) : base(message) { }
        public DataStorageException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class ConfigurationException : Exception
    {
        public ConfigurationException(string message) : base(message) { }
        public ConfigurationException(string message, Exception innerException) : base(message, innerException) { }
    }
}