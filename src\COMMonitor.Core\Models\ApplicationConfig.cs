namespace COMMonitor.Core.Models
{
    public enum DisplayMode
    {
        Text,
        Hex,
        Mixed
    }

    public class DisplayConfig
    {
        public DisplayMode DefaultDisplayMode { get; set; } = DisplayMode.Mixed;
        public bool ShowTimestamp { get; set; } = true;
        public bool ShowDirection { get; set; } = true;
        public int FontSize { get; set; } = 9;
        public string FontFamily { get; set; } = "Consolas";
        public bool AutoScroll { get; set; } = true;
        public int MaxDisplayRows { get; set; } = 10000;
    }

    public class WindowConfig
    {
        public int Width { get; set; } = 1024;
        public int Height { get; set; } = 768;
        public int X { get; set; } = 100;
        public int Y { get; set; } = 100;
        public bool Maximized { get; set; } = false;
        public Dictionary<string, int> ColumnWidths { get; set; } = new();
    }

    public class ApplicationConfig
    {
        public SerialPortConfig DefaultSerialConfig { get; set; } = new();
        public LogFileConfig LogConfig { get; set; } = new();
        public DisplayConfig DisplayConfig { get; set; } = new();
        public WindowConfig WindowConfig { get; set; } = new();
        public string Language { get; set; } = "zh-CN";
        public bool CheckForUpdates { get; set; } = true;
        public bool MinimizeToTray { get; set; } = false;
    }
}