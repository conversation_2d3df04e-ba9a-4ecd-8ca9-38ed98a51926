using System.Text.RegularExpressions;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using Microsoft.Extensions.Logging;

namespace COMMonitor.Core.Services
{
    public class DataFilterService : IDataFilterService
    {
        private readonly ILogger<DataFilterService> _logger;
        private readonly string _presetsPath;

        public DataFilterService(ILogger<DataFilterService> logger)
        {
            _logger = logger;
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var comMonitorPath = Path.Combine(appDataPath, "COMMonitor");
            Directory.CreateDirectory(comMonitorPath);
            _presetsPath = Path.Combine(comMonitorPath, "filter-presets.json");
        }

        public async Task<IReadOnlyList<SerialDataFrame>> ApplyFilterAsync(
            IEnumerable<SerialDataFrame> data, 
            FilterCriteria criteria, 
            CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (criteria == null)
                return data.ToList();

            try
            {
                var filtered = data.AsEnumerable();

                // Apply port filter
                if (!string.IsNullOrEmpty(criteria.PortName))
                {
                    filtered = filtered.Where(frame => 
                        string.Equals(frame.PortName, criteria.PortName, StringComparison.OrdinalIgnoreCase));
                }

                // Apply time range filter
                if (criteria.StartTime.HasValue)
                {
                    filtered = filtered.Where(frame => frame.Timestamp >= criteria.StartTime.Value);
                }

                if (criteria.EndTime.HasValue)
                {
                    filtered = filtered.Where(frame => frame.Timestamp <= criteria.EndTime.Value);
                }

                // Apply direction filter
                if (criteria.Direction.HasValue)
                {
                    filtered = filtered.Where(frame => frame.Direction == criteria.Direction.Value);
                }

                // Apply data pattern filter
                if (!string.IsNullOrEmpty(criteria.DataPattern))
                {
                    filtered = ApplyDataPatternFilter(filtered, criteria);
                }

                var result = filtered.ToList();
                _logger.LogInformation("Applied filter: {Count} of {Total} frames match criteria", 
                    result.Count, data.Count());

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying filter criteria");
                throw;
            }
        }

        public async Task<IReadOnlyList<SearchResult>> SearchAsync(
            IEnumerable<SerialDataFrame> data, 
            string keyword, 
            bool useRegex = false, 
            bool caseSensitive = false, 
            CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (string.IsNullOrEmpty(keyword))
                return Array.Empty<SearchResult>();

            try
            {
                var results = new List<SearchResult>();
                var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

                foreach (var frame in data)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var searchIn = frame.DataAsText;
                    var searchHex = frame.DataAsHex;

                    // Search in text representation
                    var textMatches = SearchInContent(searchIn, keyword, useRegex, caseSensitive);
                    results.AddRange(textMatches.Select(match => new SearchResult
                    {
                        Frame = frame,
                        MatchIndex = match.Index,
                        MatchText = match.Value
                    }));

                    // Search in hex representation
                    var hexMatches = SearchInContent(searchHex, keyword, useRegex, caseSensitive);
                    results.AddRange(hexMatches.Select(match => new SearchResult
                    {
                        Frame = frame,
                        MatchIndex = match.Index,
                        MatchText = match.Value
                    }));
                }

                _logger.LogInformation("Search completed: {Count} matches found for keyword '{Keyword}'", 
                    results.Count, keyword);

                return results;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Search was cancelled");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching data");
                throw;
            }
        }

        public async Task SaveFilterPresetAsync(FilterCriteria criteria, string name, CancellationToken cancellationToken = default)
        {
            if (criteria == null)
                throw new ArgumentNullException(nameof(criteria));

            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Preset name cannot be empty", nameof(name));

            try
            {
                var presets = await LoadPresetsInternalAsync(cancellationToken);
                
                // Remove existing preset with same name
                presets.RemoveAll(p => string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
                
                // Add new preset
                presets.Add(new FilterPreset
                {
                    Name = name.Trim(),
                    Criteria = criteria,
                    CreatedAt = DateTime.UtcNow
                });

                await SavePresetsInternalAsync(presets, cancellationToken);
                _logger.LogInformation("Filter preset saved: {Name}", name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving filter preset: {Name}", name);
                throw;
            }
        }

        public async Task<IReadOnlyList<FilterCriteria>> LoadFilterPresetsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var presets = await LoadPresetsInternalAsync(cancellationToken);
                return presets.Select(p => p.Criteria).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading filter presets");
                return Array.Empty<FilterCriteria>();
            }
        }

        public async Task<IReadOnlyList<string>> GetPresetNamesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var presets = await LoadPresetsInternalAsync(cancellationToken);
                return presets.Select(p => p.Name).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading preset names");
                return Array.Empty<string>();
            }
        }

        public async Task DeleteFilterPresetAsync(string name, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Preset name cannot be empty", nameof(name));

            try
            {
                var presets = await LoadPresetsInternalAsync(cancellationToken);
                var removed = presets.RemoveAll(p => string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
                
                if (removed > 0)
                {
                    await SavePresetsInternalAsync(presets, cancellationToken);
                    _logger.LogInformation("Filter preset deleted: {Name}", name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting filter preset: {Name}", name);
                throw;
            }
        }

        private IEnumerable<SerialDataFrame> ApplyDataPatternFilter(
            IEnumerable<SerialDataFrame> frames, 
            FilterCriteria criteria)
        {
            if (string.IsNullOrEmpty(criteria.DataPattern))
                return frames;

            return frames.Where(frame => 
                MatchesPattern(frame.DataAsText, criteria.DataPattern, criteria.UseRegex, criteria.CaseSensitive) ||
                MatchesPattern(frame.DataAsHex, criteria.DataPattern, criteria.UseRegex, criteria.CaseSensitive));
        }

        private bool MatchesPattern(string input, string pattern, bool useRegex, bool caseSensitive)
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(pattern))
                return false;

            try
            {
                if (useRegex)
                {
                    var options = caseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                    return Regex.IsMatch(input, pattern, options);
                }
                else
                {
                    var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                    return input.Contains(pattern, comparison);
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid regex pattern: {Pattern}", pattern);
                return false;
            }
        }

        private IEnumerable<Match> SearchInContent(string content, string keyword, bool useRegex, bool caseSensitive)
        {
            if (string.IsNullOrEmpty(content) || string.IsNullOrEmpty(keyword))
                yield break;

            List<Match> results = new List<Match>();
            
            try
            {
                if (useRegex)
                {
                    var options = caseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                    var matches = Regex.Matches(content, keyword, options);
                    foreach (Match match in matches)
                    {
                        results.Add(match);
                    }
                }
                else
                {
                    var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                    int index = 0;
                    while ((index = content.IndexOf(keyword, index, comparison)) != -1)
                    {
                        results.Add(new Match(content, index, keyword.Length));
                        index += keyword.Length;
                    }
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid regex pattern in search: {Pattern}", keyword);
            }
            
            foreach (var result in results)
            {
                yield return result;
            }
        }

        private async Task<List<FilterPreset>> LoadPresetsInternalAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (!File.Exists(_presetsPath))
                    return new List<FilterPreset>();

                var json = await File.ReadAllTextAsync(_presetsPath, cancellationToken);
                if (string.IsNullOrEmpty(json))
                    return new List<FilterPreset>();

                return System.Text.Json.JsonSerializer.Deserialize<List<FilterPreset>>(json) ?? new List<FilterPreset>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error loading filter presets from file, returning empty list");
                return new List<FilterPreset>();
            }
        }

        private async Task SavePresetsInternalAsync(List<FilterPreset> presets, CancellationToken cancellationToken)
        {
            try
            {
                var options = new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                };
                var json = System.Text.Json.JsonSerializer.Serialize(presets, options);
                await File.WriteAllTextAsync(_presetsPath, json, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving filter presets to file");
                throw;
            }
        }

        private class FilterPreset
        {
            public string Name { get; set; } = string.Empty;
            public FilterCriteria Criteria { get; set; } = new();
            public DateTime CreatedAt { get; set; }
        }

        private class Match
        {
            public string Value { get; }
            public int Index { get; }
            public int Length { get; }

            public Match(string value, int index, int length)
            {
                Value = value.Substring(index, length);
                Index = index;
                Length = length;
            }
        }
    }
}