# COM Monitor Test Runner Script
# This script runs all tests for the filtering functionality

Write-Host "=== COM Monitor Filter Functionality Test Runner ===" -ForegroundColor Green
Write-Host "Running comprehensive tests for filtering system..." -ForegroundColor Yellow

# Change to the project directory
Set-Location $PSScriptRoot

# Build the project
Write-Host "`n1. Building the project..." -ForegroundColor Cyan
dotnet build --configuration Release --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Run unit tests
Write-Host "`n2. Running unit tests..." -ForegroundColor Cyan
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj --configuration Release --no-build --logger "console;verbosity=normal"
if ($LASTEXITCODE -ne 0) {
    Write-Host "Unit tests failed!" -ForegroundColor Red
    exit 1
}

# Run integration tests
Write-Host "`n3. Running integration tests..." -ForegroundColor Cyan
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj --configuration Release --no-build --filter Category=Integration --logger "console;verbosity=normal"
if ($LASTEXITCODE -ne 0) {
    Write-Host "Integration tests failed!" -ForegroundColor Red
    exit 1
}

# Run performance tests
Write-Host "`n4. Running performance benchmarks..." -ForegroundColor Cyan
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj --configuration Release --no-build --filter Category=Performance --logger "console;verbosity=normal"

# Generate coverage report
Write-Host "`n5. Generating coverage report..." -ForegroundColor Cyan
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj --configuration Release --no-build --collect:"XPlat Code Coverage" --results-directory:"TestResults"

# Display summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "✓ Build completed successfully" -ForegroundColor Green
Write-Host "✓ Unit tests passed" -ForegroundColor Green
Write-Host "✓ Integration tests passed" -ForegroundColor Green
Write-Host "✓ Performance benchmarks completed" -ForegroundColor Green
Write-Host "✓ Coverage report generated" -ForegroundColor Green

# Show coverage if reportgenerator is available
if (Get-Command "reportgenerator" -ErrorAction SilentlyContinue) {
    Write-Host "`n6. Processing coverage report..." -ForegroundColor Cyan
    $coverageFiles = Get-ChildItem "TestResults" -Recurse -Filter "coverage.cobertura.xml" | Select-Object -First 1
    if ($coverageFiles) {
        reportgenerator "-reports:$($coverageFiles.FullName)" "-targetdir:TestResults\CoverageReport" "-reporttypes:Html"
        Write-Host "Coverage report available at: TestResults\CoverageReport\index.html" -ForegroundColor Green
    }
}

Write-Host "`n🎉 All tests completed successfully!" -ForegroundColor Green
Write-Host "The filtering functionality is ready for production use." -ForegroundColor Yellow