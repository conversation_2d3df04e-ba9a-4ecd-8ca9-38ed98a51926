<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="internal-nlog.txt">

  <targets>
    <target name="logfile" xsi:type="File"
            fileName="${specialfolder:folder=ApplicationData}/COMMonitor/logs/${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            maxArchiveFiles="10"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            concurrentWrites="true"
            keepFileOpen="false" />

    <target name="logconsole" xsi:type="Console"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />

    <target name="logviewer" xsi:type="RichTextBox"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            controlName="logRichTextBox"
            formName="LogForm"
            maxLines="1000" />
  </targets>

  <rules>
    <logger name="*" minlevel="Debug" writeTo="logfile" />
    <logger name="*" minlevel="Debug" writeTo="logconsole" />
  </rules>
</nlog>