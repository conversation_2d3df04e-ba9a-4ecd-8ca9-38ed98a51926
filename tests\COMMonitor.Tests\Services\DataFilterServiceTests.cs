using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using COMMonitor.Core.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;

namespace COMMonitor.Tests.Services
{
    public class DataFilterServiceTests
    {
        private readonly Mock<ILogger<DataFilterService>> _loggerMock;
        private readonly DataFilterService _service;

        public DataFilterServiceTests()
        {
            _loggerMock = new Mock<ILogger<DataFilterService>>();
            _service = new DataFilterService(_loggerMock.Object);
        }

        private List<SerialDataFrame> CreateTestData()
        {
            var testTime = DateTime.Now;
            return new List<SerialDataFrame>
            {
                new SerialDataFrame
                {
                    Timestamp = testTime.AddMinutes(-5),
                    Data = Encoding.ASCII.GetBytes("Hello World"),
                    Direction = DataDirection.Receive,
                    PortName = "COM1",
                    SequenceNumber = 1
                },
                new SerialDataFrame
                {
                    Timestamp = testTime.AddMinutes(-3),
                    Data = Encoding.ASCII.GetBytes("Test Data 123"),
                    Direction = DataDirection.Transmit,
                    PortName = "COM2",
                    SequenceNumber = 2
                },
                new SerialDataFrame
                {
                    Timestamp = testTime.AddMinutes(-1),
                    Data = Encoding.ASCII.GetBytes("hello test"),
                    Direction = DataDirection.Receive,
                    PortName = "COM1",
                    SequenceNumber = 3
                },
                new SerialDataFrame
                {
                    Timestamp = testTime,
                    Data = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, // "Hello" in hex
                    Direction = DataDirection.Transmit,
                    PortName = "COM3",
                    SequenceNumber = 4
                }
            };
        }

        [Fact]
        public async Task ApplyFilterAsync_EmptyCriteria_ReturnsAllData()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria();

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Equal(data.Count, result.Count);
        }

        [Fact]
        public async Task ApplyFilterAsync_ByPortName_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria { PortName = "COM1" };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, frame => Assert.Equal("COM1", frame.PortName));
        }

        [Fact]
        public async Task ApplyFilterAsync_ByDirection_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria { Direction = DataDirection.Receive };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, frame => Assert.Equal(DataDirection.Receive, frame.Direction));
        }

        [Fact]
        public async Task ApplyFilterAsync_ByTimeRange_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var testTime = DateTime.Now;
            var criteria = new FilterCriteria
            {
                StartTime = testTime.AddMinutes(-4),
                EndTime = testTime.AddMinutes(-2)
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Single(result);
            Assert.Equal("Test Data 123", Encoding.ASCII.GetString(result[0].Data));
        }

        [Fact]
        public async Task ApplyFilterAsync_ByDataPattern_CaseInsensitive_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria
            {
                DataPattern = "hello",
                CaseSensitive = false
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Contains(result, frame => Encoding.ASCII.GetString(frame.Data).Contains("Hello"));
            Assert.Contains(result, frame => Encoding.ASCII.GetString(frame.Data).Contains("hello"));
        }

        [Fact]
        public async Task ApplyFilterAsync_ByDataPattern_CaseSensitive_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria
            {
                DataPattern = "Hello",
                CaseSensitive = true
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Single(result);
            Assert.Equal("Hello World", Encoding.ASCII.GetString(result[0].Data));
        }

        [Fact]
        public async Task ApplyFilterAsync_ByDataPattern_Regex_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria
            {
                DataPattern = @"\b\w+\s\w+\b",
                UseRegex = true
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Equal(3, result.Count); // All except the hex data frame
        }

        [Fact]
        public async Task ApplyFilterAsync_CombinedCriteria_FiltersCorrectly()
        {
            // Arrange
            var data = CreateTestData();
            var testTime = DateTime.Now;
            var criteria = new FilterCriteria
            {
                PortName = "COM1",
                Direction = DataDirection.Receive,
                DataPattern = "hello",
                CaseSensitive = false
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Single(result);
            Assert.Equal("COM1", result[0].PortName);
            Assert.Equal(DataDirection.Receive, result[0].Direction);
            Assert.Contains("hello", Encoding.ASCII.GetString(result[0].Data));
        }

        [Fact]
        public async Task SearchAsync_EmptyKeyword_ReturnsEmptyList()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = "";

            // Act
            var result = await _service.SearchAsync(data, keyword);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task SearchAsync_ByTextKeyword_FindsMatches()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = "Test";

            // Act
            var result = await _service.SearchAsync(data, keyword);

            // Assert
            Assert.Single(result);
            Assert.Contains("Test", result[0].MatchText);
        }

        [Fact]
        public async Task SearchAsync_ByHexKeyword_FindsMatches()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = "48 65 6C 6C 6F";

            // Act
            var result = await _service.SearchAsync(data, keyword);

            // Assert
            Assert.Single(result);
            Assert.Contains("48 65 6C 6C 6F", result[0].MatchText);
        }

        [Fact]
        public async Task SearchAsync_WithRegex_FindsMatches()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = @"\b\d+\b";

            // Act
            var result = await _service.SearchAsync(data, keyword, useRegex: true);

            // Assert
            Assert.Single(result);
            Assert.Equal("123", result[0].MatchText);
        }

        [Fact]
        public async Task SearchAsync_CaseSensitive_FindsMatches()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = "Hello";

            // Act
            var result = await _service.SearchAsync(data, keyword, caseSensitive: true);

            // Assert
            Assert.Equal(2, result.Count); // "Hello World" and hex "Hello"
        }

        [Fact]
        public async Task SaveFilterPresetAsync_ValidCriteria_SavesSuccessfully()
        {
            // Arrange
            var criteria = new FilterCriteria
            {
                PortName = "COM1",
                DataPattern = "test"
            };
            var presetName = "TestPreset";

            // Act
            await _service.SaveFilterPresetAsync(criteria, presetName);

            // Assert
            var presets = await _service.LoadFilterPresetsAsync();
            Assert.Contains(presets, p => p.PortName == "COM1" && p.DataPattern == "test");
        }

        [Fact]
        public async Task SaveFilterPresetAsync_DuplicateName_OverwritesExisting()
        {
            // Arrange
            var criteria1 = new FilterCriteria { PortName = "COM1" };
            var criteria2 = new FilterCriteria { PortName = "COM2" };
            var presetName = "TestPreset";

            // Act
            await _service.SaveFilterPresetAsync(criteria1, presetName);
            await _service.SaveFilterPresetAsync(criteria2, presetName);

            // Assert
            var presets = await _service.LoadFilterPresetsAsync();
            var preset = Assert.Single(presets);
            Assert.Equal("COM2", preset.PortName);
        }

        [Fact]
        public async Task LoadFilterPresetsAsync_ReturnsSavedPresets()
        {
            // Arrange
            var criteria1 = new FilterCriteria { PortName = "COM1" };
            var criteria2 = new FilterCriteria { PortName = "COM2" };
            await _service.SaveFilterPresetAsync(criteria1, "Preset1");
            await _service.SaveFilterPresetAsync(criteria2, "Preset2");

            // Act
            var presets = await _service.LoadFilterPresetsAsync();

            // Assert
            Assert.Equal(2, presets.Count);
            Assert.Contains(presets, p => p.PortName == "COM1");
            Assert.Contains(presets, p => p.PortName == "COM2");
        }

        [Fact]
        public async Task DeleteFilterPresetAsync_ExistingPreset_DeletesSuccessfully()
        {
            // Arrange
            var criteria = new FilterCriteria { PortName = "COM1" };
            var presetName = "TestPreset";
            await _service.SaveFilterPresetAsync(criteria, presetName);

            // Act
            await _service.DeleteFilterPresetAsync(presetName);

            // Assert
            var presets = await _service.LoadFilterPresetsAsync();
            Assert.DoesNotContain(presets, p => p.PortName == "COM1");
        }

        [Fact]
        public async Task DeleteFilterPresetAsync_NonExistingPreset_NoError()
        {
            // Arrange
            var presetName = "NonExistingPreset";

            // Act & Assert
            await _service.DeleteFilterPresetAsync(presetName); // Should not throw
        }

        [Fact]
        public async Task ApplyFilterAsync_InvalidRegexPattern_HandlesGracefully()
        {
            // Arrange
            var data = CreateTestData();
            var criteria = new FilterCriteria
            {
                DataPattern = "[invalid",
                UseRegex = true
            };

            // Act
            var result = await _service.ApplyFilterAsync(data, criteria);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task SearchAsync_InvalidRegexPattern_HandlesGracefully()
        {
            // Arrange
            var data = CreateTestData();
            string keyword = "[invalid";

            // Act
            var result = await _service.SearchAsync(data, keyword, useRegex: true);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task ApplyFilterAsync_LargeDataset_PerformsWithinTimeLimit()
        {
            // Arrange
            var data = new List<SerialDataFrame>();
            for (int i = 0; i < 10000; i++)
            {
                data.Add(new SerialDataFrame
                {
                    Timestamp = DateTime.Now.AddSeconds(-i),
                    Data = Encoding.ASCII.GetBytes($"Test data {i}"),
                    Direction = i % 2 == 0 ? DataDirection.Receive : DataDirection.Transmit,
                    PortName = $"COM{i % 10}",
                    SequenceNumber = i
                });
            }

            var criteria = new FilterCriteria
            {
                PortName = "COM1",
                DataPattern = "Test"
            };

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await _service.ApplyFilterAsync(data, criteria);
            stopwatch.Stop();

            // Assert
            Assert.NotEmpty(result);
            Assert.True(stopwatch.ElapsedMilliseconds < 2000, $"Filtering took {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public async Task GetPresetNamesAsync_ReturnsCorrectNames()
        {
            // Arrange
            var criteria1 = new FilterCriteria { PortName = "COM1" };
            var criteria2 = new FilterCriteria { PortName = "COM2" };
            await _service.SaveFilterPresetAsync(criteria1, "Preset1");
            await _service.SaveFilterPresetAsync(criteria2, "Preset2");

            // Act
            var names = await _service.GetPresetNamesAsync();

            // Assert
            Assert.Equal(2, names.Count);
            Assert.Contains("Preset1", names);
            Assert.Contains("Preset2", names);
        }
    }
}