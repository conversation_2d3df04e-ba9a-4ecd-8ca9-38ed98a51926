# COM Monitor - 串口监控工具

## 项目介绍

COM Monitor 是一个专业的串口通信监控工具，专为Windows平台设计，为开发者和测试人员提供实时、可视化的串口数据监控和分析能力。

## 功能特性

### 核心功能
- **实时监控**: 实时捕获和显示串口数据传输
- **多端口支持**: 支持多种串口配置参数
- **数据记录**: 完整的数据记录和保存功能
- **多种显示模式**: 十六进制、文本、混合显示模式
- **时间戳记录**: 精确的时间戳记录功能
- **数据过滤**: 强大的数据过滤和搜索功能
- **日志管理**: 自动日志文件管理

### 技术特性
- **高性能**: 支持1MB/s以上的数据处理能力
- **稳定性**: 24小时连续运行无崩溃
- **易用性**: 直观的用户界面，30分钟上手
- **可扩展性**: 模块化设计，易于扩展

## 系统要求

- **操作系统**: Windows 10/11
- **运行时**: .NET 6.0 或更高版本
- **内存**: 最低256MB可用内存
- **存储**: 100MB可用磁盘空间

## 安装和运行

### 开发环境

1. 安装 Visual Studio 2022 或更高版本
2. 安装 .NET 6.0 SDK
3. 克隆项目代码

```bash
git clone [project-url]
cd COMMonitor
```

### 构建项目

```bash
# 还原NuGet包
dotnet restore

# 构建解决方案
dotnet build

# 运行应用程序
dotnet run --project src\COMMonitor.UI\COMMonitor.UI.csproj
```

### 发布应用

```bash
# 发布为独立应用程序
dotnet publish src\COMMonitor.UI\COMMonitor.UI.csproj -c Release -r win-x64 --self-contained true

# 发布为框架依赖应用程序
dotnet publish src\COMMonitor.UI\COMMonitor.UI.csproj -c Release -r win-x64 --self-contained false
```

## 项目结构

```
COMMonitor/
├── src/
│   ├── COMMonitor.Core/           # 核心库
│   │   ├── Models/               # 数据模型
│   │   ├── Interfaces/           # 接口定义
│   │   ├── Services/             # 核心服务
│   │   └── Exceptions/           # 异常定义
│   ├── COMMonitor.UI/            # WinForms界面
│   │   ├── ViewModels/           # 视图模型
│   │   ├── Services/             # UI服务
│   │   └── MainForm.cs           # 主窗体
│   └── COMMonitor.Infrastructure/ # 基础设施
│       └── Services/             # 数据存储服务
├── tests/
│   └── COMMonitor.Tests/         # 单元测试
└── COMMonitor.sln               # 解决方案文件
```

## 使用指南

### 快速开始

1. **启动应用**: 运行 COMMonitor.UI.exe
2. **选择端口**: 从下拉列表中选择要监控的COM端口
3. **配置参数**: 设置波特率、数据位、校验位等参数
4. **开始监控**: 点击"开始监控"按钮
5. **查看数据**: 实时查看接收到的数据

### 数据记录

- **自动记录**: 启动监控时自动开始记录
- **手动保存**: 使用"保存"按钮手动保存当前数据
- **日志管理**: 通过日志查看器管理历史记录

### 数据分析

- **过滤功能**: 使用搜索框进行数据过滤
- **显示模式**: 切换十六进制、文本或混合显示模式
- **时间戳**: 精确的时间戳便于数据分析

## 配置说明

配置文件位于: `%APPDATA%\COMMonitor\config.json`

### 配置项

```json
{
  "defaultSerialConfig": {
    "portName": "COM1",
    "baudRate": 9600,
    "dataBits": 8,
    "stopBits": 1,
    "parity": 0
  },
  "logConfig": {
    "logDirectory": "Documents\\COMMonitor",
    "maxFileSize": 104857600,
    "maxFileCount": 100,
    "format": 0
  },
  "displayConfig": {
    "defaultDisplayMode": 2,
    "showTimestamp": true,
    "showDirection": true,
    "fontSize": 9,
    "fontFamily": "Consolas"
  }
}
```

## 开发文档

### 架构设计

- **三层架构**: 表示层、业务逻辑层、数据访问层
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection
- **异步编程**: 全面使用async/await模式
- **事件驱动**: 基于事件的消息通信

### 扩展开发

#### 添加新的串口服务

```csharp
public interface ICustomSerialPortService : ISerialPortService
{
    // 添加自定义方法
}

public class CustomSerialPortService : ICustomSerialPortService
{
    // 实现自定义逻辑
}
```

#### 添加新的数据显示模式

```csharp
public enum DisplayMode
{
    Text,
    Hex,
    Mixed,
    Custom // 新的显示模式
}
```

### 测试

运行单元测试:

```bash
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj
```

生成测试覆盖率报告:

```bash
dotnet test tests\COMMonitor.Tests\COMMonitor.Tests.csproj --collect:"XPlat Code Coverage"
```

## 故障排除

### 常见问题

1. **端口无法打开**
   - 检查端口是否被其他程序占用
   - 确认用户有访问权限
   - 验证串口参数是否正确

2. **数据不显示**
   - 检查是否已建立连接
   - 确认显示模式设置正确
   - 查看是否有数据到达

3. **日志文件问题**
   - 检查磁盘空间是否充足
   - 确认日志目录有写入权限
   - 查看日志文件是否被其他程序锁定

### 日志文件

日志文件位于: `%APPDATA%\COMMonitor\logs\`

### 获取帮助

- 查看帮助文档: 帮助 > 关于
- 查看日志文件获取详细错误信息
- 提交Issue到项目仓库

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 技术支持

如有问题，请通过以下方式联系我们：
- 项目仓库: [GitHub Issues]
- 邮件支持: <EMAIL>