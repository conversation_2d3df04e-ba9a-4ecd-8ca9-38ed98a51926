using System.Runtime.InteropServices;
using System.Reflection;

// 程序集基本信息
[assembly: AssemblyTitle("COM Monitor - Serial Port Monitor")]
[assembly: AssemblyDescription("Professional serial port monitoring and debugging tool for Windows")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Open Source Project")]
[assembly: AssemblyProduct("COM Monitor")]
[assembly: AssemblyCopyright("Copyright © 2024 Open Source")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// COM 设置
[assembly: ComVisible(false)]

// 生成一个真实的GUID而不是占位符
[assembly: Guid("8B5C9D2E-4F7A-4B8C-9E3D-1A2B3C4D5E6F")]

// 版本信息
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("1.0.0")]

// 安全属性 - 明确标识这是一个合法的应用程序
[assembly: System.Security.AllowPartiallyTrustedCallers]