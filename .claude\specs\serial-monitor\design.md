# COM串口监控工具设计文档

## 1. 设计概述

### 1.1 设计目标
本设计旨在构建一个高性能、可扩展的COM串口监控工具，通过分层架构实现业务逻辑与UI的解耦，确保系统的可维护性和可测试性。

### 1.2 设计原则
- **单一职责原则**: 每个类和组件只负责特定的功能
- **开闭原则**: 系统对扩展开放，对修改关闭
- **依赖倒置原则**: 依赖于抽象而非具体实现
- **接口隔离原则**: 使用小而专一的接口

## 2. 系统架构

### 2.1 整体架构
采用经典的三层架构模式：

```
┌─────────────────────────────────────────┐
│              表示层 (UI)                │
│         (Windows Forms)                 │
├─────────────────────────────────────────┤
│              业务逻辑层                 │
│     (Services, ViewModels)              │
├─────────────────────────────────────────┤
│              数据访问层                 │
│     (Serial Ports, File I/O)            │
└─────────────────────────────────────────┘
```

### 2.2 技术栈
- **UI框架**: Windows Forms (.NET 6.0)
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **日志记录**: NLog
- **序列化**: System.Text.Json
- **并发处理**: System.Threading.Channels
- **异步编程**: async/await with CancellationToken

## 3. 核心组件设计

### 3.1 串口通信模块

#### 3.1.1 接口设计
```csharp
public interface ISerialPortService
{
    Task<bool> OpenAsync(SerialPortConfig config);
    Task CloseAsync();
    Task SendDataAsync(byte[] data);
    event EventHandler<DataReceivedEventArgs> DataReceived;
    event EventHandler<SerialErrorEventArgs> ErrorOccurred;
    bool IsOpen { get; }
    SerialPortStatus Status { get; }
}

public class SerialPortConfig
{
    public string PortName { get; set; }
    public int BaudRate { get; set; }
    public int DataBits { get; set; }
    public StopBits StopBits { get; set; }
    public Parity Parity { get; set; }
    public Handshake Handshake { get; set; }
}
```

#### 3.1.2 实现类设计
```csharp
public class SerialPortService : ISerialPortService, IDisposable
{
    private readonly SerialPort _serialPort;
    private readonly ILogger<SerialPortService> _logger;
    private readonly Channel<byte[]> _dataChannel;
    private CancellationTokenSource _cts;
    
    public SerialPortService(ILogger<SerialPortService> logger)
    {
        _serialPort = new SerialPort();
        _logger = logger;
        _dataChannel = Channel.CreateUnbounded<byte[]>();
    }
    
    public async Task<bool> OpenAsync(SerialPortConfig config)
    {
        // 实现串口打开逻辑
    }
    
    public async Task CloseAsync()
    {
        // 实现串口关闭逻辑
    }
    
    private async Task ProcessReceivedDataAsync(CancellationToken cancellationToken)
    {
        // 实现数据接收处理逻辑
    }
}
```

### 3.2 数据管理模块

#### 3.2.1 数据模型
```csharp
public class SerialDataFrame
{
    public DateTime Timestamp { get; set; }
    public byte[] Data { get; set; }
    public DataDirection Direction { get; set; } // Rx or Tx
    public string PortName { get; set; }
    public long SequenceNumber { get; set; }
}

public class LogFileConfig
{
    public string LogDirectory { get; set; } = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
        "COMMonitor");
    public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100MB
    public int MaxFileCount { get; set; } = 100;
    public LogFormat Format { get; set; } = LogFormat.Text;
}

public enum LogFormat
{
    Text,
    Hex,
    Mixed,
    Json
}
```

#### 3.2.2 数据存储服务
```csharp
public interface IDataStorageService
{
    Task StartLoggingAsync(LogFileConfig config);
    Task StopLoggingAsync();
    Task LogDataAsync(SerialDataFrame frame);
    Task<IEnumerable<string>> GetLogFilesAsync();
    Task<Stream> OpenLogFileAsync(string fileName);
}

public class FileStorageService : IDataStorageService
{
    private readonly ILogger<FileStorageService> _logger;
    private readonly SemaphoreSlim _fileLock = new(1, 1);
    private StreamWriter _currentWriter;
    private LogFileConfig _currentConfig;
    
    public async Task LogDataAsync(SerialDataFrame frame)
    {
        await _fileLock.WaitAsync();
        try
        {
            // 实现数据写入逻辑
        }
        finally
        {
            _fileLock.Release();
        }
    }
}
```

### 3.3 数据显示模块

#### 3.3.1 显示模式接口
```csharp
public interface IDataDisplayService
{
    string FormatData(SerialDataFrame frame, DisplayMode mode);
    string FormatHexData(byte[] data);
    string FormatTextData(byte[] data);
    string FormatMixedData(byte[] data);
}

public enum DisplayMode
{
    Text,
    Hex,
    Mixed
}
```

#### 3.3.2 数据过滤器
```csharp
public interface IDataFilterService
{
    Task<FilterResult> ApplyFilterAsync(IEnumerable<SerialDataFrame> data, FilterCriteria criteria);
    Task<IEnumerable<SerialDataFrame>> SearchAsync(string keyword, SearchOptions options);
}

public class FilterCriteria
{
    public string PortName { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public DataDirection? Direction { get; set; }
    public string DataPattern { get; set; }
    public bool CaseSensitive { get; set; }
}
```

### 3.4 UI架构设计

#### 3.4.1 主窗口结构
```csharp
public partial class MainForm : Form
{
    private readonly MainViewModel _viewModel;
    private readonly ISerialPortService _serialPortService;
    private readonly IDataStorageService _storageService;
    
    public MainForm(
        MainViewModel viewModel,
        ISerialPortService serialPortService,
        IDataStorageService storageService)
    {
        InitializeComponent();
        _viewModel = viewModel;
        _serialPortService = serialPortService;
        _storageService = storageService;
        
        InitializeDataBindings();
        SetupEventHandlers();
    }
}
```

#### 3.4.2 ViewModel设计
```csharp
public class MainViewModel : INotifyPropertyChanged
{
    private readonly ISerialPortService _serialPortService;
    private readonly IDataStorageService _storageService;
    
    public ObservableCollection<SerialDataFrame> DataFrames { get; }
    public ObservableCollection<string> AvailablePorts { get; }
    
    private SerialPortConfig _currentConfig;
    public SerialPortConfig CurrentConfig
    {
        get => _currentConfig;
        set => SetProperty(ref _currentConfig, value);
    }
    
    private bool _isMonitoring;
    public bool IsMonitoring
    {
        get => _isMonitoring;
        set => SetProperty(ref _isMonitoring, value);
    }
    
    public ICommand StartMonitoringCommand { get; }
    public ICommand StopMonitoringCommand { get; }
    public ICommand SaveLogCommand { get; }
    public ICommand ApplyFilterCommand { get; }
}
```

## 4. 数据模型设计

### 4.1 配置数据模型
```csharp
public class ApplicationConfig
{
    public SerialPortConfig DefaultSerialConfig { get; set; }
    public LogFileConfig LogConfig { get; set; }
    public DisplayConfig DisplayConfig { get; set; }
    public WindowConfig WindowConfig { get; set; }
}

public class DisplayConfig
{
    public DisplayMode DefaultDisplayMode { get; set; } = DisplayMode.Mixed;
    public bool ShowTimestamp { get; set; } = true;
    public bool ShowDirection { get; set; } = true;
    public int FontSize { get; set; } = 9;
    public string FontFamily { get; set; } = "Consolas";
}

public class WindowConfig
{
    public Size Size { get; set; }
    public Point Location { get; set; }
    public FormWindowState WindowState { get; set; }
    public Dictionary<string, int> ColumnWidths { get; set; }
}
```

### 4.2 运行时数据
```csharp
public class MonitoringSession
{
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string PortName { get; set; }
    public SerialPortConfig Config { get; set; }
    public long TotalBytesReceived { get; set; }
    public long TotalBytesSent { get; set; }
    public int ErrorCount { get; set; }
}
```

## 5. 错误处理设计

### 5.1 异常分类
- **通信异常**: 串口连接失败、端口被占用等
- **数据异常**: 数据格式错误、缓冲区溢出等
- **文件异常**: 日志文件写入失败、磁盘空间不足等
- **UI异常**: 界面更新失败、资源泄露等

### 5.2 异常处理策略
```csharp
public class GlobalExceptionHandler
{
    public static void Register()
    {
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        Application.ThreadException += OnThreadException;
    }
    
    private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        LogException(e.ExceptionObject as Exception);
        ShowErrorDialog("发生未处理的异常，程序将关闭。");
        Application.Exit();
    }
}

public class SerialPortException : Exception
{
    public SerialPortErrorCode ErrorCode { get; }
    
    public SerialPortException(SerialPortErrorCode errorCode, string message) 
        : base(message)
    {
        ErrorCode = errorCode;
    }
}

public enum SerialPortErrorCode
{
    PortNotFound,
    PortInUse,
    AccessDenied,
    InvalidConfiguration,
    Timeout,
    BufferOverflow
}
```

### 5.3 恢复机制
- **自动重连**: 连接断开后自动重试连接
- **数据缓存**: 临时缓存数据，避免数据丢失
- **日志记录**: 详细记录异常信息便于调试
- **用户通知**: 通过状态栏和消息框通知用户

## 6. 性能优化策略

### 6.1 内存管理
- 使用对象池重用SerialDataFrame对象
- 限制UI显示的数据条数（最多10000条）
- 定期清理旧数据
- 使用ValueTask减少异步开销

### 6.2 并发处理
- 使用System.Threading.Channels处理数据流
- 串口读取和数据处理分离
- UI更新使用BeginInvoke避免阻塞
- 文件写入使用异步IO

### 6.3 缓存策略
- 配置信息缓存到内存
- 端口列表缓存避免频繁扫描
- 过滤器结果缓存
- 字体和颜色资源缓存

## 7. 测试策略

### 7.1 单元测试
- 串口配置验证测试
- 数据格式化测试
- 过滤算法测试
- 异常处理测试

### 7.2 集成测试
- 端到端数据流测试
- 多端口并发测试
- 长时间运行稳定性测试
- 大文件处理测试

### 7.3 UI测试
- 界面响应性测试
- 数据绑定正确性测试
- 快捷键功能测试
- 异常对话框测试

## 8. 部署设计

### 8.1 安装程序
- 使用WiX Toolset创建MSI安装包
- 包含所有依赖项
- 创建开始菜单快捷方式
- 注册文件关联

### 8.2 更新机制
- 使用ClickOnce部署
- 支持自动更新检查
- 增量更新减少下载量
- 回滚机制确保稳定性

### 8.3 配置管理
- 配置文件存储在用户的AppData目录
- 支持导入/导出配置
- 版本兼容性检查
- 迁移机制处理配置升级