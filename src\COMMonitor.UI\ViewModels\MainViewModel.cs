using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using COMMonitor.Core.Interfaces;
using COMMonitor.Core.Models;
using COMMonitor.Core.Services;
using Microsoft.Extensions.Logging;

namespace COMMonitor.UI.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly ISerialPortService _serialPortService;
        private readonly IDataStorageService _storageService;
        private readonly IConfigurationService _configService;
        private readonly IDataFilterService _filterService;
        private readonly ILogger<MainViewModel> _logger;
        private bool _disposed;

        public ObservableCollection<SerialDataFrame> DataFrames { get; } = new();
        public ObservableCollection<SerialDataFrame> FilteredDataFrames { get; } = new();
        public ObservableCollection<string> AvailablePorts { get; } = new();

        private SerialPortConfig _currentConfig = new();
        public SerialPortConfig CurrentConfig
        {
            get => _currentConfig;
            set
            {
                if (SetProperty(ref _currentConfig, value))
                {
                    OnPropertyChanged(nameof(CanConnect));
                }
            }
        }

        private bool _isMonitoring;
        public bool IsMonitoring
        {
            get => _isMonitoring;
            set
            {
                if (SetProperty(ref _isMonitoring, value))
                {
                    OnPropertyChanged(nameof(CanConnect));
                    OnPropertyChanged(nameof(CanStartMonitoring));
                }
            }
        }

        private bool _isLogging;
        public bool IsLogging
        {
            get => _isLogging;
            set
            {
                if (SetProperty(ref _isLogging, value))
                {
                    OnPropertyChanged(nameof(IsLogging));
                }
            }
        }

        private bool _isFiltering;
        public bool IsFiltering
        {
            get => _isFiltering;
            set
            {
                if (SetProperty(ref _isFiltering, value))
                {
                    OnPropertyChanged(nameof(IsFiltering));
                    OnPropertyChanged(nameof(FilterStatusText));
                }
            }
        }

        private FilterCriteria _currentFilter;
        public FilterCriteria CurrentFilter
        {
            get => _currentFilter;
            set
            {
                if (SetProperty(ref _currentFilter, value))
                {
                    OnPropertyChanged(nameof(CurrentFilter));
                    _ = ApplyCurrentFilterAsync();
                }
            }
        }

        private long _totalBytesReceived;
        public long TotalBytesReceived
        {
            get => _totalBytesReceived;
            set => SetProperty(ref _totalBytesReceived, value);
        }

        private long _totalBytesSent;
        public long TotalBytesSent
        {
            get => _totalBytesSent;
            set => SetProperty(ref _totalBytesSent, value);
        }

        private int _errorCount;
        public int ErrorCount
        {
            get => _errorCount;
            set => SetProperty(ref _errorCount, value);
        }

        private int _filteredCount;
        public int FilteredCount
        {
            get => _filteredCount;
            set
            {
                if (SetProperty(ref _filteredCount, value))
                {
                    OnPropertyChanged(nameof(FilterStatusText));
                }
            }
        }

        private int _totalCount;
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                if (SetProperty(ref _totalCount, value))
                {
                    OnPropertyChanged(nameof(FilterStatusText));
                }
            }
        }

        private DisplayMode _displayMode = DisplayMode.Mixed;
        public DisplayMode DisplayMode
        {
            get => _displayMode;
            set => SetProperty(ref _displayMode, value);
        }

        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string FilterStatusText => 
            IsFiltering ? $"过滤: {_filteredCount}/{_totalCount}" : $"总计: {_totalCount}";

        public bool CanConnect => !IsMonitoring && CurrentConfig?.IsValid() == true;
        public bool CanStartMonitoring => !IsMonitoring;

        public MainViewModel(
            ISerialPortService serialPortService,
            IDataStorageService storageService,
            IConfigurationService configService,
            IDataFilterService filterService,
            ILogger<MainViewModel> logger)
        {
            _serialPortService = serialPortService;
            _storageService = storageService;
            _configService = configService;
            _filterService = filterService;
            _logger = logger;

            _ = InitializeAsync();
        }

        ~MainViewModel()
        {
            Dispose(false);
        }

        private async Task InitializeAsync()
        {
            try
            {
                var config = await _configService.LoadConfigurationAsync();
                CurrentConfig = config.DefaultSerialConfig;
                DisplayMode = config.DisplayConfig.DefaultDisplayMode;

                _serialPortService.DataReceived += OnDataReceived;
                _serialPortService.ErrorOccurred += OnErrorOccurred;

                await RefreshAvailablePortsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing view model");
                StatusMessage = $"初始化错误: {ex.Message}";
            }
        }

        private async void OnDataReceived(object? sender, DataReceivedEventArgs e)
        {
            if (e.DataFrame == null) return;

            try
            {
                var frame = e.DataFrame;
                
                // Add to full dataset
                if (DataFrames.Count >= 10000)
                {
                    DataFrames.RemoveAt(0);
                }
                DataFrames.Add(frame);
                TotalCount = DataFrames.Count;

                // Update filtered view
                if (IsFiltering && CurrentFilter != null)
                {
                    await ApplyCurrentFilterAsync();
                }
                else
                {
                    // Keep filtered and full datasets in sync when no filter is active
                    SyncFilteredWithFull();
                }

                // Update statistics
                if (frame.Direction == DataDirection.Receive)
                {
                    TotalBytesReceived += frame.Length;
                }
                else
                {
                    TotalBytesSent += frame.Length;
                }

                // Log to file if enabled
                if (IsLogging)
                {
                    await _storageService.LogDataAsync(frame).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing received data");
            }
        }

        private void SyncFilteredWithFull()
        {
            FilteredDataFrames.Clear();
            foreach (var frame in DataFrames)
            {
                FilteredDataFrames.Add(frame);
            }
            FilteredCount = FilteredDataFrames.Count;
        }

        private async Task ApplyCurrentFilterAsync()
        {
            if (CurrentFilter == null || !CurrentFilter.IsActive)
            {
                SyncFilteredWithFull();
                return;
            }

            try
            {
                IsFiltering = true;
                var filtered = await _filterService.ApplyFilterAsync(DataFrames, CurrentFilter);
                
                FilteredDataFrames.Clear();
                foreach (var frame in filtered)
                {
                    FilteredDataFrames.Add(frame);
                }
                
                FilteredCount = FilteredDataFrames.Count;
                StatusMessage = $"过滤器已应用: {FilteredCount}/{TotalCount} 匹配";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying filter");
                StatusMessage = $"过滤错误: {ex.Message}";
                SyncFilteredWithFull(); // Fallback to showing all data
            }
        }

        private void OnErrorOccurred(object? sender, SerialErrorEventArgs e)
        {
            ErrorCount++;
            StatusMessage = $"错误: {e.Message}";
            _logger.LogError("Serial port error: {ErrorCode} - {Message}", e.ErrorCode, e.Message);
        }

        public async Task ConnectAsync()
        {
            try
            {
                StatusMessage = "正在连接...";
                var success = await _serialPortService.OpenAsync(CurrentConfig);
                
                if (success)
                {
                    StatusMessage = $"已连接到 {CurrentConfig.PortName}";
                }
                else
                {
                    StatusMessage = "连接失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to serial port");
                StatusMessage = $"连接错误: {ex.Message}";
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                StatusMessage = "正在断开连接...";
                await _serialPortService.CloseAsync();
                IsMonitoring = false;
                StatusMessage = "已断开连接";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from serial port");
                StatusMessage = $"断开连接错误: {ex.Message}";
            }
        }

        public async Task StartMonitoringAsync()
        {
            if (IsMonitoring) return;

            try
            {
                if (!_serialPortService.IsOpen)
                {
                    await ConnectAsync();
                }

                if (_serialPortService.IsOpen)
                {
                    IsMonitoring = true;
                    StatusMessage = "正在监控...";

                    if (!IsLogging)
                    {
                        var config = await _configService.LoadConfigurationAsync();
                        await _storageService.StartLoggingAsync(config.LogConfig);
                        IsLogging = _storageService.IsLogging;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting monitoring");
                StatusMessage = $"开始监控错误: {ex.Message}";
            }
        }

        public async Task StopMonitoringAsync()
        {
            if (!IsMonitoring) return;

            try
            {
                IsMonitoring = false;
                await _storageService.StopLoggingAsync();
                IsLogging = false;
                StatusMessage = "监控已停止";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping monitoring");
                StatusMessage = $"停止监控错误: {ex.Message}";
            }
        }

        public async Task RefreshAvailablePortsAsync()
        {
            try
            {
                AvailablePorts.Clear();
                var ports = await _serialPortService.GetAvailablePortsAsync();
                foreach (var port in ports.OrderBy(p => p))
                {
                    AvailablePorts.Add(port);
                }

                if (AvailablePorts.Count > 0 && string.IsNullOrEmpty(CurrentConfig.PortName))
                {
                    CurrentConfig.PortName = AvailablePorts[0];
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing available ports");
                StatusMessage = $"刷新端口错误: {ex.Message}";
            }
        }

        public void ClearDisplay()
        {
            DataFrames.Clear();
            FilteredDataFrames.Clear();
            TotalBytesReceived = 0;
            TotalBytesSent = 0;
            ErrorCount = 0;
            TotalCount = 0;
            FilteredCount = 0;
            StatusMessage = "显示已清除";
        }

        public async Task SaveConfigurationAsync()
        {
            try
            {
                var config = await _configService.LoadConfigurationAsync();
                config.DefaultSerialConfig = CurrentConfig;
                config.DisplayConfig.DefaultDisplayMode = DisplayMode;
                
                await _configService.SaveConfigurationAsync(config);
                StatusMessage = "配置已保存";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving configuration");
                StatusMessage = $"保存配置错误: {ex.Message}";
            }
        }

        public async Task ApplyFilterAsync(FilterCriteria criteria)
        {
            CurrentFilter = criteria;
            await ApplyCurrentFilterAsync();
        }

        public async Task ClearFilterAsync()
        {
            CurrentFilter = null;
            IsFiltering = false;
            SyncFilteredWithFull();
            StatusMessage = "过滤器已清除";
        }

        public async Task<IReadOnlyList<SearchResult>> SearchAsync(string keyword, bool useRegex = false, bool caseSensitive = false)
        {
            return await _filterService.SearchAsync(DataFrames, keyword, useRegex, caseSensitive);
        }

        public async Task<IReadOnlyList<SerialDataFrame>> GetFilteredDataAsync(FilterCriteria criteria)
        {
            return await _filterService.ApplyFilterAsync(DataFrames, criteria);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Unsubscribe from events to prevent memory leaks
                    if (_serialPortService != null)
                    {
                        _serialPortService.DataReceived -= OnDataReceived;
                        _serialPortService.ErrorOccurred -= OnErrorOccurred;
                    }

                    // Stop monitoring and logging
                    if (IsMonitoring)
                    {
                        _ = StopMonitoringAsync();
                    }

                    if (IsLogging)
                    {
                        _ = _storageService.StopLoggingAsync();
                    }
                }
                _disposed = true;
            }
        }
    }
}